{"version": 3, "sources": ["extension.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBhG,4BA2GC;AAzHD,+CAAiC;AACjC,gDAAkC;AAElC,MAAM,eAAe,GAAG,WAAW,CAAC;AACpC,MAAM,iBAAiB,GAAG,8BAA8B,CAAC;AACzD,MAAM,aAAa,GAAG,kCAAkC,CAAC;AACzD,MAAM,sBAAsB,GAAG,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AAC9E,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;AAC/F,MAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC;AAEhF,IAAI,eAA6F,CAAC;AAClG,IAAI,sBAAqD,CAAC;AAG1D,SAAgB,QAAQ,CAAC,OAAgC;IAExD,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAChG,MAAM,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;IAElG,MAAM,QAAQ,GAAG,CAAC,MAAyB,EAAE,EAAE;QAC9C,MAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACxE,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1F,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzF,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAC;QAC7D,MAAM,CAAC,cAAc,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEF,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,KAAK,eAAe,EAAE,CAAC;QAC9G,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED,OAAO,CAAC,aAAa,CAAC,IAAI,CAEzB,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,sBAAsB,EAAE;QACvE,sBAAsB,CAAC,QAA6B,EAAE,KAA+B;YACpF,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,EAAE,KAAK,CAAC;iBACjD,MAAM,CAAC,UAAU,CAAC;iBAClB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,cAAc,CACrC,IAAI,CAAC,IAAI,EACT,EAAE,EACF,MAAM,CAAC,UAAU,CAAC,IAAI,EACtB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAoB,EAAE,EAAE,EAAE,CAAC,oBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,oBAAqB,CAAC,EAC5I,IAAI,CAAC,QAAQ,CAAC,oBAAqB,CACnC,CAAC,CAAC;YAEJ,OAAO,OAAO,CAAC;QAChB,CAAC;KACD,CAAC,EAEF,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,sBAAsB,EAAE;QACvE,sBAAsB,CAAC,QAA6B,EAAE,QAAyB;YAE9E,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAAC,OAAO,EAAE,CAAC;YAAC,CAAC;YACrC,IAAI,QAAQ,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACjF,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAEhG,OAAO,UAAU;qBACf,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC3E,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YACtF,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAAC,OAAO,EAAE,CAAC;YAAC,CAAC;YAExD,OAAO,KAAK;iBACV,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC9C,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1D,CAAC;KACD,EAAE,GAAG,CAAC,EAEP,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,sBAAsB,EAAE;QACnE,iBAAiB,CAAC,QAA6B,EAAE,QAAyB,EAAE,KAA+B;YAC1G,MAAM,UAAU,GAAG,kBAAkB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACtE,IAAI,CAAC,UAAU,EAAE,CAAC;gBAAC,OAAO,EAAE,CAAC;YAAC,CAAC;YAC/B,IAAI,UAAU,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAChC,OAAO,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,oBAAoB,EAAE,UAAU,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;YACrH,CAAC;YAED,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3F,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACf,OAAO,EAAE,CAAC;YACX,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,QAAQ,CACpC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,EACxC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,SAAS,CAAC,CACpH,CAAC;YACF,OAAO,CAAC;oBACP,GAAG,QAAQ;oBACX,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC;iBAC5D,CAAC,CAAC;QACJ,CAAC;KACD,CAAC,EAEF,MAAM,CAAC,SAAS,CAAC,4BAA4B,CAAC,sBAAsB,EAAE;QACrE,KAAK,CAAC,oBAAoB,CAAC,QAA6B,EAAE,KAA+B;YACxF,OAAO,kBAAkB,CAAC,QAAQ,EAAE,KAAK,CAAC;iBACxC,MAAM,CAAC,UAAU,CAAC;iBAClB,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,oBAAqB,EAAE,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAClG,CAAC;KACD,CAAC,EAEF,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;QAClD,IAAI,MAAM,EAAE,QAAQ,CAAC,UAAU,KAAK,eAAe,EAAE,CAAC;YACrD,iDAAiD;YACjD,kJAAkJ;YAClJ,eAAe,GAAG,SAAS,CAAC;YAE5B,sBAAsB,EAAE,OAAO,EAAE,CAAC;YAClC,sBAAsB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE;gBACvE,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,KAAK,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;oBAC9C,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;YACF,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;IACF,CAAC,CAAC,EAEF,EAAE,OAAO,KAAK,eAAe,GAAG,SAAS,CAAC,CAAC,sBAAsB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CACjF,CAAC;AACH,CAAC;AAGD,SAAS,iBAAiB,CAAC,IAAY,EAAE,UAAsB;IAE9D,MAAM,cAAc,GAAG,aAAa,CAAC;IACrC,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;QACrC,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC/F,CAAC;IAED,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,IAAI,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACzF,CAAC;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC;QAChE,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,MAAM,qBAAqB,GAAG,CAAC,MAA8B,EAAE,IAAY,EAAc,EAAE,CAC1F,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAEvC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;QACvC,MAAM,sBAAsB,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,sBAAsB,EAAE,CAAC;YAC5B,MAAM,CAAC,EAAE,aAAa,EAAE,aAAa,CAAC,GAAG,sBAAsB,CAAC;YAChE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5F,IAAI,MAAM,EAAE,CAAC;gBACZ,OAAO,qBAAqB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YACrD,CAAC;QACF,CAAC;aACI,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzD,OAAO,qBAAqB,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC1E,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAC7C,4EAA4E;YAC5E,yIAAyI;YACzI,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3H,IAAI,WAAW,EAAE,CAAC;gBACjB,OAAO,qBAAqB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACjD,CAAC;QACF,CAAC;IACF,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC;IAChD,OAAO,SAAS,CAAC;AAClB,CAAC;AAKD,MAAM,UAAU,GAAG,CAAC,IAAmD,EAAgC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;AAC/H,MAAM,YAAY,GAAG,CAAC,IAAmD,EAAkC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;AAGrI,SAAS,kBAAkB,CAAC,QAA6B,EAAE,KAAgC;IAE1F,IAAI,eAAe,IAAI,eAAe,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,IAAI,eAAe,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC7G,OAAO,eAAe,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAChD,MAAM,KAAK,GAAwB,EAAE,CAAC;IAEtC,IAAI,aAAa,GAA2B,SAAS,CAAC;IACtD,IAAI,sBAAsB,GAAsC,SAAS,CAAC;IAE1E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,yEAAyE;QACzE,IAAI,KAAK,EAAE,uBAAuB,EAAE,CAAC;YAAC,OAAO,EAAE,CAAC;QAAC,CAAC;QAClD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,QAAQ,EAAE,CAAC;YACd,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC;YAE1B,aAAa,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,EAAE,CAAC;gBAAC,SAAS;YAAC,CAAC;YACjC,sBAAsB,GAAG,EAAE,CAAC;YAE5B,MAAM,QAAQ,GAAwB;gBACrC,WAAW,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,EAAE,aAAa;gBACxB,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;aAC5D,CAAC;YAGF,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YAAC,SAAS;QAAC,CAAC;QAEjC,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,UAAU,EAAE,CAAC;YAChB,MAAM,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC,GAAG,UAAU,CAAC;YAC3D,MAAM,UAAU,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC;YACpC,MAAM,cAAc,GAAG,CAAC,WAAW,GAAG,WAAW,GAAG,SAAS,CAAC,CAAC,MAAM,CAAC;YACtE,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAElG,MAAM,SAAS,GAAoC,EAAE,CAAC;YAEtD,IAAI,OAAO,GAAG,cAAc,CAAC;YAC7B,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,aAAa,CAAC,SAAS,GAAG,cAAc,CAAC;YACzC,KAAK,IAAI,KAA6B,EAAE,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;gBAC7E,SAAS,CAAC,IAAI,CAAC;oBACd,WAAW;oBACX,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;oBAC9E,SAAS,EAAE,aAAa;oBACxB,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,aAAa,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;iBAChG,CAAC,CAAC;gBAEH,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,OAAO,GAAG,aAAa,CAAC,SAAS,CAAC;YACnC,CAAC;YAED,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3B,SAAS,CAAC,IAAI,CAAC;oBACd,WAAW;oBACX,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;oBAC9E,SAAS,EAAE,aAAa;oBACxB,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;iBAClE,CAAC,CAAC;YACJ,CAAC;YACD,4CAA4C;YAC5C,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,sBAAsB,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;YAC5C,CAAC;YAED,yEAAyE;YACzE,MAAM,mBAAmB,GAAkC;gBAC1D,WAAW;gBACX,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;gBACpE,SAAS,EAAE,aAAa;gBACxB,oBAAoB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC;aACnE,CAAC;YACF,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,KAAK,GAAG,EAAE,WAAW,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC;QAChI,CAAC;IACF,CAAC;IAED,eAAe,GAAG;QACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;QACzB,KAAK,EAAE,KAAK;QACZ,GAAG,EAAE,QAAQ,CAAC,GAAG;KACjB,CAAC;IAEF,OAAO,KAAK,CAAC;AACd,CAAC", "file": "extension.js", "sourceRoot": "../src/"}