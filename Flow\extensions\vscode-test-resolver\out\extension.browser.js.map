{"version": 3, "sources": ["extension.browser.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIhG,4BAWC;AAbD,+CAAiC;AAEjC,SAAgB,QAAQ,CAAC,QAAiC;IACzD,MAAM,CAAC,SAAS,CAAC,+BAA+B,CAAC,MAAM,EAAE;QACxD,KAAK,CAAC,OAAO,CAAC,UAAkB;YAC/B,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,EAAE,CAAC,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;YACxE,MAAM,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,8BAA8B,CAAC,EAAE,QAAQ,EAAE,CAAC;YACjF,OAAO,IAAI,MAAM,CAAC,wBAAwB,CAAC,KAAK,IAAI,EAAE;gBACrD,OAAO,IAAI,4BAA4B,EAAE,CAAC;YAC3C,CAAC,CAAC,CAAC;QACJ,CAAC;KACD,CAAC,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,MAAM,4BAA4B;IAAlC;QACkB,gBAAW,GAAG,IAAI,MAAM,CAAC,YAAY,EAAc,CAAC;QACpD,iBAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAAqB,CAAC;QAC5D,eAAU,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;QAE9C,wBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QAC7C,eAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;QACrC,aAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QAEzC,YAAO,GAAwC,IAAI,CAAC;QACpD,gBAAW,GAAG,KAAK,CAAC;IAyC7B,CAAC;IAvCO,IAAI,CAAC,CAAa;QACxB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,kCAAkC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,OAAO;QACR,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,uCAAuC;YACvC,OAAO;QACR,CAAC;QAED,mCAAmC;QACnC,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE9B,qJAAqJ;QACrJ,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,OAAO,CAAC,KAAK,CAAC,mBAAmB,GAAG,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,mBAAmB,GAAG,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO;QACR,CAAC;QAED,wIAAwI;QACxI,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9B,sDAAsD;QACtD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,4BAA4B,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAClH,CAAC;IAEM,GAAG;QACT,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACnB,OAAO;QACR,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACzB,CAAC;CACD;AAED,MAAM,4BAA4B;IAMjC,YACC,GAAQ,EACR,WAA4C,EAC5C,YAAoD,EACpD,WAAsC;QAP/B,WAAM,GAAG,KAAK,CAAC;QACf,iBAAY,GAAiB,EAAE,CAAC;QAQvC,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC,sBAAsB,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,EAAE,2BAA2B,CAAC,EAAE,CAAC,CAAC;QAChJ,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;YACnD,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;YACzC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAG,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YAEnB,gDAAgD;YAChD,yDAAyD;YACzD,0CAA0C;YAC1C,sEAAsE;YACtE,+CAA+C;YAC/C,MAAM,eAAe,GAAG;gBACvB,kCAAkC;gBAClC,oBAAoB;gBACpB,qBAAqB;gBACrB,4BAA4B;aAC5B,CAAC;YACF,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;YACtC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC;YAC9D,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACJ,CAAC;IAEM,IAAI,CAAC,CAAa;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,OAAO;QACR,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IAEM,GAAG;QACT,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;CACD", "file": "extension.browser.js", "sourceRoot": "../src/"}