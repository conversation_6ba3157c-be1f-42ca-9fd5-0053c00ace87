{"version": 3, "sources": ["dropOrPaste/uriList.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AAEjC,SAAS,YAAY,CAAC,GAAW;IAChC,OAAO,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAChC,OAAO,YAAY,CAAC,GAAG,CAAC;SACtB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB;SAC1D,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC9B,CAAC;AAED,MAAa,OAAO;IAEnB,MAAM,CAAC,IAAI,CAAC,GAAW;QACtB,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACxD,IAAI,CAAC;gBACJ,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;YACnD,CAAC;YAAC,MAAM,CAAC;gBACR,oBAAoB;gBACpB,OAAO,SAAS,CAAC;YAClB,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IAED,YACiB,OAA0E;QAA1E,YAAO,GAAP,OAAO,CAAmE;IACvF,CAAC;CACL;AAhBD,0BAgBC;AAED,SAAS,QAAQ,CAAI,KAA0C;IAC9D,OAAY,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC", "file": "uriList.js", "sourceRoot": "../../src/"}