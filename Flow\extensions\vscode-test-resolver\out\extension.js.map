{"version": 3, "sources": ["extension.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBhG,4BA4ZC;AAjbD,+CAAiC;AACjC,kDAAoC;AACpC,2CAA6B;AAC7B,uCAAyB;AACzB,uCAAyB;AACzB,yCAA2B;AAC3B,2CAA6B;AAC7B,+CAAiC;AACjC,yCAA0D;AAC1D,gDAAoD;AAEpD,IAAI,cAA2C,CAAC;AAMhD,IAAI,aAAmC,CAAC;AAExC,MAAM,4BAA4B,GAAG,GAAG,CAAC;AAEzC,SAAgB,QAAQ,CAAC,OAAgC;IAExD,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,MAAM,qBAAqB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAW,CAAC;IAEjE,IAAI,oBAAoB,GAAG,KAAK,CAAC;IACjC,MAAM,yBAAyB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAW,CAAC;IACrE,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAY,CAAC;IAClD,yBAAyB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,KAAK,MAAM,EAAE,IAAI,qBAAqB,EAAE,CAAC;gBACxC,EAAE,EAAE,CAAC;YACN,CAAC;YACD,qBAAqB,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC;IACF,CAAC,CAAC,CAAC;IAEH,SAAS,iBAAiB;QACzB,OAAO;YACN,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAC7F;oBACC,EAAE,EAAE,QAAQ;oBACZ,KAAK,EAAE,QAAQ;oBACf,SAAS,EAAE,KAAK;iBAChB;gBACD;oBACC,EAAE,EAAE,OAAO;oBACX,KAAK,EAAE,OAAO;oBACd,SAAS,EAAE,eAAe;iBAC1B;gBACD;oBACC,EAAE,EAAE,SAAS;oBACb,KAAK,EAAE,SAAS;oBAChB,SAAS,EAAE,YAAY;iBACvB;aACD,CAAC,CAAC,CAAC,EAAE;SACN,CAAC;IACH,CAAC;IAED,SAAS,aAAa;QACrB,IAAI,oBAAoB,EAAE,CAAC;YAC1B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC5B,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC9B,OAAO,EAAE,CAAC;oBACV,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACvC,CAAC,EAAE,4BAA4B,CAAC,CAAC;gBAEjC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;oBAC9B,OAAO,EAAE,CAAC;oBACV,YAAY,CAAC,MAAM,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;QACJ,CAAC;IACF,CAAC;IAED,SAAS,SAAS,CAAC,SAAiB,EAAE,QAAmE;QACxG,IAAI,gBAAgB,EAAE,CAAC;YACtB,MAAM,MAAM,CAAC,4BAA4B,CAAC,uBAAuB,CAAC,yBAAyB,CAAC,CAAC;QAC9F,CAAC;QACD,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;QAE/D,qDAAqD;QACrD,MAAM,aAAa,GAAG,IAAI,OAAO,CAA2B,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YAC9E,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;YACvD,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAElE,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,KAAK,UAAU,YAAY,CAAC,OAAe;gBAC1C,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAClC,IAAI,CAAC,UAAU,EAAE,CAAC;oBACjB,UAAU,GAAG,IAAI,CAAC;oBAClB,aAAa,CAAC,IAAI,EAAE,CAAC;oBAErB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;oBAC/F,IAAI,MAAM,EAAE,CAAC;wBACZ,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;oBACxB,CAAC;oBACD,GAAG,CAAC,MAAM,CAAC,4BAA4B,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACtE,CAAC;YACF,CAAC;YAED,IAAI,gBAAgB,GAAG,EAAE,CAAC;YAC1B,SAAS,aAAa,CAAC,MAAc;gBACpC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACxC,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACjC,IAAI,GAAG,+BAAsB,EAAE,CAAC;wBAC/B,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;wBAChF,IAAI,KAAK,EAAE,CAAC;4BACX,UAAU,GAAG,IAAI,CAAC;4BAClB,GAAG,CAAC,IAAI,MAAM,CAAC,iBAAiB,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW;wBACrG,CAAC;wBACD,gBAAgB,GAAG,EAAE,CAAC;oBACvB,CAAC;yBAAM,IAAI,GAAG,+BAAuB,EAAE,CAAC;wBACvC,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC5E,CAAC;yBAAM,CAAC;wBACP,gBAAgB,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACtC,CAAC;gBACF,CAAC;YACF,CAAC;YACD,MAAM,KAAK,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAC/C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC/B,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjC,aAAa,CAAC,MAAM,CAAC,uBAAuB,SAAS,uDAAuD,CAAC,CAAC;gBAC9G,OAAO,SAAS,GAAG,CAAC,EAAE,CAAC;oBACtB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,gCAAgC,SAAS,GAAG,EAAE,CAAC,CAAC;oBAC3E,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpB,SAAS,EAAE,CAAC;gBACb,CAAC;YACF,CAAC;YAED,IAAI,gBAAgB,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC/C,YAAY,CAAC,wFAAwF,CAAC,CAAC;gBACvG,OAAO;YACR,CAAC;YAED,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,cAAc,EAAE,GAAG,uBAAuB,EAAE,CAAC;YAC9H,MAAM,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,+BAA+B,CAAC,CAAC;YACjI,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC;YACxB,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,GAAG,oBAAoB,IAAI,cAAc,eAAe,CAAC,CAAC;YACnJ,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxD,IAAI,OAAO,EAAE,CAAC;gBACb,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACzC,CAAC;YACD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACvD,IAAI,QAAQ,EAAE,CAAC;gBACd,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACrC,CAAC;YACD,aAAa,CAAC,UAAU,CAAC,wBAAwB,aAAa,EAAE,CAAC,CAAC;YAClE,WAAW,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;YAErD,WAAW,CAAC,IAAI,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;YAExD,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,WAAW;gBACzB,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC;gBAC1F,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC9E,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBAE1E,aAAa,CAAC,UAAU,CAAC,sBAAsB,iBAAiB,KAAK,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC9F,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC;gBAC7C,cAAc,GAAG,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5F,CAAC;iBAAM,CAAC;gBACP,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACjF,IAAI,kBAAkB,EAAE,CAAC;oBACxB,WAAW,CAAC,IAAI,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,CAAC;oBACpE,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACpC,CAAC;gBACD,MAAM,aAAa,GAAG,GAAG,qBAAqB,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC9F,IAAI,cAAc,GAAG,GAAG,CAAC,2BAA2B,CAAC,CAAC,CAAC,qEAAqE;gBAC5H,IAAI,CAAC,cAAc,EAAE,CAAC;oBACrB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;oBAClD,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;oBACzD,cAAc,GAAG,MAAM,IAAA,uCAA4B,EAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9H,CAAC;gBAED,aAAa,CAAC,UAAU,CAAC,yBAAyB,cAAc,EAAE,CAAC,CAAC;gBACpE,aAAa,CAAC,UAAU,CAAC,oBAAoB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACtE,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC;gBAC7C,cAAc,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa,CAAC,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC;YAC9H,CAAC;YACD,cAAc,CAAC,MAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACpF,cAAc,CAAC,MAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACpF,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;gBAC3C,YAAY,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC5D,cAAc,GAAG,SAAS,CAAC;YAC5B,CAAC,CAAC,CAAC;YACH,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,EAAE;gBAC3C,YAAY,CAAC,4CAA4C,IAAI,EAAE,CAAC,CAAC;gBACjE,cAAc,GAAG,SAAS,CAAC;YAC5B,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,GAAG,EAAE;oBACb,IAAI,cAAc,EAAE,CAAC;wBACpB,IAAA,4BAAgB,EAAC,cAAc,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;oBACzD,CAAC;gBACF,CAAC;aACD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,UAAU,EAAkC,EAAE;YACxE,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAClD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,wBAAwB,CAAC,KAAK,IAAI,EAAE;oBACrE,MAAM,YAAY,GAAG,GAAG,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;oBACrE,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,YAAY,EAAc,CAAC;oBAC1D,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,YAAY,EAAqB,CAAC;oBAClE,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;oBAEnD,MAAM,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBAC9B,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;6BAC/C,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;6BACtD,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;6BACpC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;6BAClC,EAAE,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC;oBAGH,OAAO;wBACN,mBAAmB,EAAE,WAAW,CAAC,KAAK;wBACtC,UAAU,EAAE,YAAY,CAAC,KAAK;wBAC9B,QAAQ,EAAE,UAAU,CAAC,KAAK;wBAC1B,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;wBAChC,GAAG,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE;qBAC7B,CAAC;gBACH,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC;YACtB,CAAC;YAED,OAAO,IAAI,OAAO,CAA2B,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAC1D,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;oBAClD,aAAa,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;oBACtD,IAAI,WAAW,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,CAAC;oBAC1C,MAAM,YAAY,GAAG,GAAG,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;oBAErE,IAAI,cAAc,GAAG,KAAK,CAAC;oBAC3B,MAAM,qBAAqB,GAAG,GAAG,EAAE;wBAClC,MAAM,iBAAiB,GAAG,gBAAgB,CAAC;wBAC3C,IAAI,cAAc,KAAK,iBAAiB,EAAE,CAAC;4BAC1C,aAAa,CAAC,UAAU,CAAC,qBAAqB,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;4BACvF,cAAc,GAAG,iBAAiB,CAAC;4BACnC,IAAI,CAAC,cAAc,EAAE,CAAC;gCACrB,aAAa,CAAC,UAAU,CAAC,kCAAkC,CAAC,CAAC;gCAC7D,IAAI,YAAY,CAAC,QAAQ,EAAE,IAAI,UAAU,EAAE,CAAC;oCAC3C,YAAY,CAAC,MAAM,EAAE,CAAC;gCACvB,CAAC;gCACD,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,WAAW,EAAE,CAAC;oCAC3C,WAAW,CAAC,MAAM,EAAE,CAAC;gCACtB,CAAC;4BACF,CAAC;iCAAM,CAAC;gCACP,aAAa,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;gCAC9D,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;oCAC9B,YAAY,CAAC,KAAK,EAAE,CAAC;gCACtB,CAAC;gCACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC;oCAC7B,WAAW,CAAC,KAAK,EAAE,CAAC;gCACrB,CAAC;4BACF,CAAC;wBACF,CAAC;oBACF,CAAC,CAAC;oBAEF,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,CAAC;oBAC1D,qBAAqB,EAAE,CAAC;oBAExB,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;wBACrC,MAAM,aAAa,EAAE,CAAC;wBACtB,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACvC,IAAI,CAAC,WAAW,EAAE,CAAC;4BAClB,WAAW,CAAC,KAAK,EAAE,CAAC;wBACrB,CAAC;oBACF,CAAC,CAAC,CAAC;oBACH,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;wBACtC,MAAM,aAAa,EAAE,CAAC;wBACtB,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACrC,IAAI,CAAC,UAAU,EAAE,CAAC;4BACjB,YAAY,CAAC,KAAK,EAAE,CAAC;wBACtB,CAAC;oBACF,CAAC,CAAC,CAAC;oBACH,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;wBAC5B,UAAU,GAAG,IAAI,CAAC;wBAClB,IAAI,CAAC,cAAc,EAAE,CAAC;4BACrB,YAAY,CAAC,MAAM,EAAE,CAAC;wBACvB,CAAC;oBACF,CAAC,CAAC,CAAC;oBACH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;wBAC7B,WAAW,GAAG,IAAI,CAAC;wBACnB,IAAI,CAAC,cAAc,EAAE,CAAC;4BACrB,WAAW,CAAC,MAAM,EAAE,CAAC;wBACtB,CAAC;oBACF,CAAC,CAAC,CAAC;oBACH,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;wBAC5B,aAAa,CAAC,UAAU,CAAC,6CAA6C,CAAC,CAAC;wBACxE,YAAY,CAAC,GAAG,EAAE,CAAC;oBACpB,CAAC,CAAC,CAAC;oBACH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;wBAC7B,aAAa,CAAC,UAAU,CAAC,6CAA6C,CAAC,CAAC;wBACxE,WAAW,CAAC,GAAG,EAAE,CAAC;oBACnB,CAAC,CAAC,CAAC;oBACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;wBAC1B,OAAO,EAAE,GAAG,EAAE;4BACb,WAAW,CAAC,GAAG,EAAE,CAAC;4BAClB,YAAY,CAAC,GAAG,EAAE,CAAC;wBACpB,CAAC;qBACD,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC;gBACH,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE;oBACvC,MAAM,IAAI,GAAqB,WAAW,CAAC,OAAO,EAAG,CAAC,IAAI,CAAC;oBAC3D,aAAa,CAAC,UAAU,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;oBAChE,GAAG,CAAC,IAAI,MAAM,CAAC,iBAAiB,CAAC,WAAW,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,GAAG,EAAE;wBACb,WAAW,CAAC,KAAK,EAAE,CAAC;oBACrB,CAAC;iBACD,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,2BAA2B,GAAG,MAAM,CAAC,SAAS,CAAC,+BAA+B,CAAC,MAAM,EAAE;QAC5F,KAAK,CAAC,eAAe,CAAC,GAAe;YACpC,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;QACD,OAAO,CAAC,UAAkB;YACzB,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBACjC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,2EAA2E;gBAClF,WAAW,EAAE,KAAK;aAClB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACrB,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBACjD,EAAE,CAAC,cAAc,GAAG,iBAAiB,EAAE,CAAC;gBACxC,OAAO,EAAE,CAAC;YACX,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,aAAa;QACb,iBAAiB;KACjB,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAExD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAChG,OAAO,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,EAAE,eAAe,EAAE,WAAW,EAAE,CAAC,CAAC;IAC7F,CAAC,CAAC,CAAC,CAAC;IACJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACpG,OAAO,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAChH,CAAC,CAAC,CAAC,CAAC;IACJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0CAA0C,EAAE,GAAG,EAAE;QAC3G,OAAO,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,EAAE,eAAe,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IACnH,CAAC,CAAC,CAAC,CAAC;IACJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACzG,OAAO,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,EAAE,eAAe,EAAE,YAAY,EAAE,CAAC,CAAC;IAC9F,CAAC,CAAC,CAAC,CAAC;IACJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sDAAsD,EAAE,GAAG,EAAE;QACvH,2BAA2B,CAAC,OAAO,EAAE,CAAC;QACtC,IAAI,cAAc,EAAE,CAAC;YACpB,IAAA,4BAAgB,EAAC,cAAc,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QACzD,CAAC;QACD,MAAM,CAAC,SAAS,CAAC,+BAA+B,CAAC,MAAM,EAAE;YACxD,KAAK,CAAC,OAAO,CAAC,UAAkB;gBAC/B,UAAU,CAAC,KAAK,IAAI,EAAE;oBACrB,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACjH,CAAC,EAAE,IAAI,CAAC,CAAC;gBACT,MAAM,MAAM,CAAC,4BAA4B,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;YACnF,CAAC;SACD,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC;IACJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC9F,IAAI,aAAa,EAAE,CAAC;YACnB,aAAa,CAAC,IAAI,EAAE,CAAC;QACtB,CAAC;IACF,CAAC,CAAC,CAAC,CAAC;IAEJ,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC9F,mBAAmB,CAAC,IAAI,GAAG,yCAAyC,CAAC;IACrE,mBAAmB,CAAC,OAAO,GAAG,2CAA2C,CAAC;IAC1E,mBAAmB,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;IAE7F,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2CAA2C,EAAE,GAAG,EAAE;QAC5G,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACvB,gBAAgB,GAAG,IAAI,CAAC;YACxB,mBAAmB,CAAC,IAAI,EAAE,CAAC;QAC5B,CAAC;aAAM,CAAC;YACP,gBAAgB,GAAG,KAAK,CAAC;YACzB,mBAAmB,CAAC,IAAI,EAAE,CAAC;QAC5B,CAAC;QACD,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC,CAAC;IAEJ,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACjG,sBAAsB,CAAC,IAAI,GAAG,8CAA8C,CAAC;IAC7E,sBAAsB,CAAC,OAAO,GAAG,8CAA8C,CAAC;IAChF,sBAAsB,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;IAEhG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8CAA8C,EAAE,GAAG,EAAE;QAC/G,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3B,oBAAoB,GAAG,IAAI,CAAC;YAC5B,sBAAsB,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;aAAM,CAAC;YACP,oBAAoB,GAAG,KAAK,CAAC;YAC7B,sBAAsB,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;QACD,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC,CAAC;IAEJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QACvG,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC/C,MAAM,EAAE,sCAAsC;YAC9C,KAAK,EAAE,MAAM;YACb,aAAa,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB;SAChF,CAAC,CAAC;QACH,IAAI,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBAC3B,aAAa,EAAE;oBACd,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,IAAI;iBACV;gBACD,gBAAgB,EAAE,IAAI,GAAG,CAAC;aAC1B,CAAC,CAAC;QACJ,CAAC;IAEF,CAAC,CAAC,CAAC,CAAC;IACJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;QAC9G,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC/C,MAAM,EAAE,sCAAsC;YAC9C,KAAK,EAAE,MAAM;YACb,aAAa,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB;SAChF,CAAC,CAAC;QACH,IAAI,MAAM,EAAE,CAAC;YACZ,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5C,CAAC;IAEF,CAAC,CAAC,CAAC,CAAC;IACJ,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,2BAA2B,EAAE,IAAI,CAAC,CAAC;AACjF,CAAC;AAID,SAAS,UAAU;IAClB,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,KAAK,UAAU,CAAC;IAE9J,OAAO,CAAC,IAAI,CAAC;QACZ,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,KAAK,IAAI,EAAE;YACnB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;QACvE,CAAC;KACD,CAAC,CAAC;IACH,IAAI,CAAC,OAAO,EAAE,CAAC;QACd,OAAO,CAAC,IAAI,CAAC;YACZ,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;YACxG,CAAC;SACD,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,CAAC,IAAI,CAAC;QACZ,KAAK,EAAE,QAAQ;QACf,iBAAiB,EAAE,IAAI;QACvB,OAAO,EAAE,KAAK,IAAI,EAAE;YACnB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC,CAAC,kBAAkB;QAClF,CAAC;KACD,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AAChB,CAAC;AAWD,SAAS,uBAAuB;IAC/B,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC1F,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAA0B,CAAC;AACrD,CAAC;AAED,SAAS,SAAS;IACjB,MAAM,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAC/B,OAAO,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACnC,OAAO,GAAG,CAAC;AACZ,CAAC;AAED,SAAS,KAAK,CAAC,EAAU;IACxB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAC5B,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAI,EAAU;IACtC,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,GAAG,CAAI,EAAE,CAAC,CAAC;AACrE,CAAC;AAED,MAAM,aAAa,GAAa,EAAE,CAAC;AAEnC,KAAK,UAAU,iBAAiB,CAAC,KAAa,EAAE,IAAY,EAAE,OAAe;IAC5E,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC;AACrD,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,aAAmC,EAAE,qBAAmD;IACpH,aAAa,CAAC,UAAU,CAAC,kCAAkC,aAAa,CAAC,aAAa,CAAC,IAAI,aAAa,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAC1I,IAAI,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;QAC7C,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yFAAyF,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;IAC9J,CAAC;IAED,OAAO,mBAAmB,EAAE,CAAC;IAE7B,SAAS,SAAS,CAAC,YAA4C;QAC9D,MAAM,YAAY,GAA8B,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;QAC1E,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,OAAO;YACN,YAAY;YACZ,aAAa,EAAE,aAAa,CAAC,aAAa;YAC1C,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,aAAa,CAAC,MAAM;YAC7G,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,YAAY,EAAE,YAAY,CAAC,KAAK;YAChC,OAAO,EAAE,GAAG,EAAE;gBACb,IAAI,CAAC,UAAU,EAAE,CAAC;oBACjB,UAAU,GAAG,IAAI,CAAC;oBAClB,YAAY,CAAC,IAAI,EAAE,CAAC;gBACrB,CAAC;YACF,CAAC;SACD,CAAC;IACH,CAAC;IAED,SAAS,mBAAmB;QAC3B,OAAO,IAAI,OAAO,CAAgB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC/C,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;gBAClD,MAAM,YAAY,GAAG,GAAG,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC9H,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC/B,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YACH,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,IAAI,aAAa,CAAC,gBAAgB,EAAE,CAAC;gBACpC,yEAAyE;gBACzE,iHAAiH;gBACjH,4CAA4C;gBAC5C,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACP,SAAS,GAAG,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC;YAC9C,CAAC;YAED,IAAI,SAAS,KAAK,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBACpD,SAAS,IAAI,CAAC,CAAC;YAChB,CAAC;YAED,iFAAiF;YACjF,IAAI,SAAS,GAAG,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACtD,SAAS,GAAG,CAAC,CAAC;YACf,CAAC;YACD,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE;gBAC/C,MAAM,SAAS,GAAqB,WAAW,CAAC,OAAO,EAAG,CAAC,IAAI,CAAC;gBAChE,aAAa,CAAC,UAAU,CAAC,4CAA4C,aAAa,CAAC,aAAa,CAAC,IAAI,aAAa,SAAS,EAAE,CAAC,CAAC;gBAC/H,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;gBACjE,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC/C,GAAG,CAAC,MAAM,CAAC,CAAC;YACb,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC;AACF,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAY;IACtC,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;QAC9C,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACnB,GAAG,CAAC,GAAG,CAAC,iDAAiD,IAAI,GAAG,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IACH,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACjC,MAAM,OAAO,GAAG,0CAA0C,IAAI,EAAE,CAAC;IACjE,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrB,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO;QACN,OAAO,EAAE,GAAG,EAAE;YACb,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBAClB,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAChC,CAAC;QACF,CAAC;KACD,CAAC;AACH,CAAC", "file": "extension.js", "sourceRoot": "../src/"}