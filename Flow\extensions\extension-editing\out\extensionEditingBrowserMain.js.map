{"version": 3, "sources": ["extensionEditingBrowserMain.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKhG,4BAIC;AAPD,+CAAiC;AACjC,mEAA0D;AAE1D,SAAgB,QAAQ,CAAC,OAAgC;IACxD,0BAA0B;IAC1B,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,kCAAkC,EAAE,CAAC,CAAC;AAElE,CAAC;AAED,SAAS,kCAAkC;IAC1C,OAAO,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,EAAE;QACxG,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK;YAC/C,OAAO,IAAI,uCAAe,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;KACD,CAAC,CAAC;AAEJ,CAAC", "file": "extensionEditingBrowserMain.js", "sourceRoot": "../src/"}