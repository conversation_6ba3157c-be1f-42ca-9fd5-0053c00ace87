{"version": 3, "sources": ["colorizer.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,uCAAyB;AACzB,iBAAe;AACf,+BAAiD;AACjD,mCAAuE;AAavE,SAAS,kBAAkB,CAAC,OAAqG;IAChI,IAAI,SAA6B,CAAC;IAClC,IAAI,WAA+B,CAAC;IACpC,IAAI,YAAgC,CAAC;IACrC,IAAI,YAAgC,CAAC;IACrC,IAAI,UAA8B,CAAC;IACnC,IAAI,YAAgC,CAAC;IACrC,IAAI,aAAiC,CAAC;IACtC,IAAI,aAAiC,CAAC;IAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACnE,cAAc;YACd,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;YAC7E,IAAI,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,GAAG,SAAS,EAAE,CAAC;gBAC7D,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;YAC9B,CAAC;YACD,IAAI,WAAW,KAAK,SAAS,IAAI,MAAM,CAAC,WAAW,GAAG,WAAW,EAAE,CAAC;gBACnE,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;YAClC,CAAC;YACD,IAAI,YAAY,KAAK,SAAS,IAAI,MAAM,CAAC,YAAY,GAAG,YAAY,EAAE,CAAC;gBACtE,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;YACpC,CAAC;YACD,IAAI,YAAY,KAAK,SAAS,IAAI,QAAQ,GAAG,YAAY,EAAE,CAAC;gBAC3D,YAAY,GAAG,QAAQ,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACb,IAAI,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,GAAG,UAAU,EAAE,CAAC;oBAC/D,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;gBAC/B,CAAC;gBACD,IAAI,YAAY,KAAK,SAAS,IAAI,MAAM,CAAC,WAAW,GAAG,YAAY,EAAE,CAAC;oBACrE,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC;gBACnC,CAAC;gBACD,IAAI,aAAa,KAAK,SAAS,IAAI,MAAM,CAAC,YAAY,GAAG,aAAa,EAAE,CAAC;oBACxE,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;gBACrC,CAAC;gBACD,IAAI,aAAa,KAAK,SAAS,IAAI,QAAQ,GAAG,aAAa,EAAE,CAAC;oBAC7D,aAAa,GAAG,QAAQ,CAAC;gBAC1B,CAAC;YACF,CAAC;QACF,CAAC;aAAM,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAChC,WAAW;YACX,IAAI,YAAY,KAAK,SAAS,IAAI,MAAM,CAAC,YAAY,GAAG,YAAY,EAAE,CAAC;gBACtE,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,MAAM,CAAC,YAAY,GAAG,aAAa,CAAC,EAAE,CAAC;gBACrF,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;YACrC,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO;QACN,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,EAAE,YAAa;QAC3B,UAAU;QACV,YAAY;QACZ,aAAa;QACb,aAAa,EAAE,aAAc;KAC7B,CAAC;AACH,CAAC;AAYD,KAAK,UAAU,UAAU,CAA8C,OAAe,EAAE,IAAS,EAAE,KAAa;IAC/G,MAAM,OAAO,GAAgB,EAAE,CAAC;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,OAAO,CAAC,IAAI,CAAC,MAAM,iBAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IACD,OAAO,OAAO,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,MAAM,CAAC,IAAS,EAAE,KAAa;IAC7C,MAAM,iBAAiB,GAAG,MAAM,UAAU,CAAkB,qCAAqC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAEhH,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;IAC7J,MAAM,eAAe,GAAG,MAAM,UAAU,CAAgB,mCAAmC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1G,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,eAAe,CAAC,CAAC;IAE9D,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,UAAkB,EAAE,EAAE;QACrD,yCAAyC;QACzC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC,CAAC;IACF,MAAM,SAAS,GAAG,CAAC,CAAC;IACpB,MAAM,YAAY,GAAG;;4BAEM,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,SAAU,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,UAAW,EAAE,SAAS,CAAC;4BAC9H,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,WAAY,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,YAAa,EAAE,SAAS,CAAC;4BACpI,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,YAAa,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,aAAc,EAAE,SAAS,CAAC;4BACvI,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,YAAY,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,aAAa,EAAE,SAAS,CAAC;4BACzM,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,iBAAiB,CAAC,YAAY,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,iBAAiB,CAAC,aAAa,EAAE,SAAS,CAAC;CAClM,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAA,eAAQ,EAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC3B,CAAC;AAED,KAAK,CAAC,0BAA0B,EAAE,GAAG,EAAE;IACtC,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,IAAA,WAAI,EAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IACvD,MAAM,YAAY,GAAG,IAAA,WAAI,EAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;IACzD,IAAI,oBAAyB,CAAC;IAE9B,UAAU,CAAC,KAAK;QACf,oBAAoB,GAAG,kBAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QACjG,MAAM,kBAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,+BAA+B,EAAE,CAAC,YAAY,CAAC,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;IAChI,CAAC,CAAC,CAAC;IACH,aAAa,CAAC,KAAK;QAClB,MAAM,kBAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,+BAA+B,EAAE,oBAAoB,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;IACtI,CAAC,CAAC,CAAC;IAEH,KAAK,MAAM,OAAO,IAAI,EAAE,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;QACpD,IAAI,CAAC,uBAAuB,OAAO,EAAE,EAAE,KAAK;YAC3C,MAAM,iBAAQ,CAAC,cAAc,CAAC,kCAAkC,CAAC,CAAC;YAClE,MAAM,MAAM,CAAC,YAAG,CAAC,IAAI,CAAC,IAAA,WAAI,EAAC,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACJ,CAAC;AACF,CAAC,CAAC,CAAC", "file": "colorizer.test.js", "sourceRoot": "../src/"}