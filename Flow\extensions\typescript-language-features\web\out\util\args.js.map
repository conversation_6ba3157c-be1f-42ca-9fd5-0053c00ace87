{"version": 3, "sources": ["util/args.ts"], "names": [], "mappings": ";;;AAMA,kCAEC;AAED,oCAKC;AAED,0DAGC;AAWD,0CAUC;AAnCD,SAAgB,WAAW,CAAC,IAAuB,EAAE,IAAY;IAChE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAED,SAAgB,YAAY,CAAC,IAAuB,EAAE,IAAY;IACjE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,OAAO,CAAC,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;QAC3C,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,SAAS,CAAC;AACd,CAAC;AAED,SAAgB,uBAAuB,CAAC,IAAuB,EAAE,IAAY;IAC5E,MAAM,GAAG,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACrC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;AAC5E,CAAC;AAED;;GAEG;AACH,IAAY,mBAIX;AAJD,WAAY,mBAAmB;IAC9B,qEAAY,CAAA;IACZ,mFAAmB,CAAA;IACnB,uEAAa,CAAA;AACd,CAAC,EAJW,mBAAmB,mCAAnB,mBAAmB,QAI9B;AAED,SAAgB,eAAe,CAAC,IAAuB;IACtD,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAChD,IAAI,CAAC,IAAI,EAAE,CAAC;QAAC,OAAO,SAAS,CAAC;IAAC,CAAC;IAEhC,QAAQ,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;QAC5B,KAAK,UAAU,CAAC,CAAC,OAAO,mBAAmB,CAAC,QAAQ,CAAC;QACrD,KAAK,iBAAiB,CAAC,CAAC,OAAO,mBAAmB,CAAC,eAAe,CAAC;QACnE,KAAK,WAAW,CAAC,CAAC,OAAO,mBAAmB,CAAC,SAAS,CAAC;QACvD,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;IACtB,CAAC;AACF,CAAC", "file": "args.js", "sourceRoot": "../../src/"}