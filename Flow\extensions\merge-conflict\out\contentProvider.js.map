{"version": 3, "sources": ["contentProvider.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AAEjC,MAAqB,4BAA4B;IAIhD,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;IACpD,CAAC;IAED,KAAK;QACJ,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAC9B,MAAM,CAAC,SAAS,CAAC,mCAAmC,CAAC,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,CAC/F,CAAC;IACH,CAAC;IAED,OAAO;IACP,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,GAAe;QAC/C,IAAI,CAAC;YACJ,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAiH,CAAC;YAEjK,gBAAgB;YAChB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YAE1F,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,IAAI,YAAY,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACzB,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC;gBAC5C,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,aAAa,CAAC;gBACnC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;gBAEvC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC3H,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;gBACjG,YAAY,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;YACtE,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;YAE/H,OAAO,IAAI,CAAC;QACb,CAAC;QACD,OAAO,EAAE,EAAE,CAAC;YACX,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QACb,CAAC;IACF,CAAC;;AA3CM,mCAAM,GAAG,8BAA8B,CAAC;kBAF3B,4BAA4B", "file": "contentProvider.js", "sourceRoot": "../src/"}