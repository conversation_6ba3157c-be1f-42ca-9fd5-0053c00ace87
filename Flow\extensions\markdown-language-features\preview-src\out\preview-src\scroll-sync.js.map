{"version": 3, "sources": ["preview-src/scroll-sync.ts", "../scroll-sync.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AA8DhG,4DAaC;AAKD,kEAyBC;AAuBD,4DA6BC;AAED,4EAiBC;AAKD,8DAIC;AArLD,MAAM,aAAa,GAAG,WAAW,CAAC;AAGlC,MAAa,eAAe;IAG3B,YACU,OAAoB,EACpB,IAAY,EACZ,WAAyB;QAFzB,YAAO,GAAP,OAAO,CAAa;QACpB,SAAI,GAAJ,IAAI,CAAQ;QACZ,gBAAW,GAAX,WAAW,CAAc;QAElC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAqB,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;IACxG,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;CACD;AAdD,0CAcC;AAED,MAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE;IACjC,IAAI,cAA6C,CAAC;IAClD,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;IACvB,OAAO,CAAC,eAAuB,EAAE,EAAE;QAClC,IAAI,CAAC,cAAc,IAAI,eAAe,KAAK,aAAa,EAAE,CAAC;YAC1D,aAAa,GAAG,eAAe,CAAC;YAChC,cAAc,GAAG,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,sBAAsB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACtE,IAAI,CAAC,CAAC,OAAO,YAAY,WAAW,CAAC,EAAE,CAAC;oBACvC,SAAS;gBACV,CAAC;gBAED,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAE,CAAC;gBACjD,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjB,SAAS;gBACV,CAAC;gBAGD,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBACpG,oFAAoF;oBACpF,2DAA2D;oBAC3D,cAAc,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;gBAChF,CAAC;qBAAM,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;oBACjE,mGAAmG;gBACpG,CAAC;qBAAM,CAAC;oBACP,cAAc,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACzD,CAAC;YACF,CAAC;QACF,CAAC;QACD,OAAO,cAAc,CAAC;IACvB,CAAC,CAAC;AACH,CAAC,CAAC,EAAE,CAAC;AAEL;;;;;GAKG;AACH,SAAgB,wBAAwB,CAAC,UAAkB,EAAE,eAAuB;IACnF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC1C,MAAM,KAAK,GAAG,mBAAmB,CAAC,eAAe,CAAC,CAAC;IACnD,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAChC,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;QAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC7C,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,GAAG,UAAU,EAAE,CAAC;YACpC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QAClC,CAAC;QACD,QAAQ,GAAG,KAAK,CAAC;IAClB,CAAC;IACD,OAAO,EAAE,QAAQ,EAAE,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAgB,2BAA2B,CAAC,MAAc,EAAE,eAAuB;IAClF,MAAM,KAAK,GAAG,mBAAmB,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAC5E,MAAM,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IACzC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IACZ,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;YAC5C,EAAE,GAAG,GAAG,CAAC;QACV,CAAC;aACI,CAAC;YACL,EAAE,GAAG,GAAG,CAAC;QACV,CAAC;IACF,CAAC;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC7C,IAAI,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC;QACxC,MAAM,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5B,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IACjD,CAAC;IACD,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;QAC9E,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;IACrD,CAAC;IACD,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAChC,CAAC;AAED,SAAS,gBAAgB,CAAC,EAAE,OAAO,EAAmB;IACrD,MAAM,QAAQ,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;IAEjD,gEAAgE;IAChE,yDAAyD;IACzD,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC;IACjE,IAAI,aAAa,EAAE,CAAC;QACnB,MAAM,WAAW,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,OAAO;YACN,GAAG,EAAE,QAAQ,CAAC,GAAG;YACjB,MAAM,EAAE,MAAM;SACd,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,IAAY,EAAE,eAAuB,EAAE,eAAgC;IAC/G,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,uBAAuB,EAAE,CAAC;QACxD,OAAO;IACR,CAAC;IAED,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACjC,OAAO;IACR,CAAC;IAED,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,wBAAwB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAC3E,IAAI,CAAC,QAAQ,EAAE,CAAC;QACf,OAAO;IACR,CAAC;IACD,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACxC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;IAC7B,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzC,8DAA8D;QAC9D,MAAM,eAAe,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7E,MAAM,WAAW,GAAG,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,GAAG,GAAG,WAAW,CAAC;QAC7E,QAAQ,GAAG,WAAW,GAAG,eAAe,GAAG,aAAa,CAAC;IAC1D,CAAC;SAAM,CAAC;QACP,MAAM,iBAAiB,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,QAAQ,GAAG,WAAW,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,iBAAiB,CAAC,CAAC;IAC5D,CAAC;IACD,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IACnE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,SAAgB,gCAAgC,CAAC,MAAc,EAAE,eAAuB;IACvF,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,2BAA2B,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IAChF,IAAI,QAAQ,EAAE,CAAC;QACd,IAAI,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,CAAC,CAAC;QACV,CAAC;QACD,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,kBAAkB,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;QAC1E,IAAI,IAAI,EAAE,CAAC;YACV,MAAM,uBAAuB,GAAG,kBAAkB,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;YACvG,OAAO,QAAQ,CAAC,IAAI,GAAG,uBAAuB,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACP,MAAM,qBAAqB,GAAG,kBAAkB,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC3E,OAAO,QAAQ,CAAC,IAAI,GAAG,qBAAqB,CAAC;QAC9C,CAAC;IACF,CAAC;IACD,OAAO,IAAI,CAAC;AACb,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CAAC,QAAgB,EAAE,eAAuB;IAClF,OAAO,mBAAmB,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;QAC5D,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,QAAQ,CAAC;IACxC,CAAC,CAAC,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,qBAAqB,CAAwB,OAAoB,EAAE,OAAe;IAC3F,KAAK,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAChF,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;YAChC,MAAM,MAAW,CAAC;QACnB,CAAC;IACF,CAAC;AACF,CAAC", "file": "scroll-sync.js", "sourceRoot": "../../src/"}