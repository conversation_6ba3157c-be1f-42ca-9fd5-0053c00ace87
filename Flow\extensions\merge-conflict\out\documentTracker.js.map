{"version": 3, "sources": ["documentTracker.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAGhG,+DAA4D;AAE5D,uCAAoC;AAGpC,MAAM,QAAQ;IAIb,YAAY,SAAiB,EAAE,aAAqB;QAH7C,YAAO,GAAgB,IAAI,GAAG,EAAU,CAAC;QAI/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,iBAAO,CAAsC,SAAS,CAAC,CAAC;IAC9E,CAAC;IAEM,SAAS,CAAC,IAAY;QAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAEM,SAAS,CAAC,IAAY;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;CACD;AAED,MAAM,kCAAkC;IACvC,YAAoB,MAAoC,EAAU,MAAc;QAA5D,WAAM,GAAN,MAAM,CAA8B;QAAU,WAAM,GAAN,MAAM,CAAQ;IAChF,CAAC;IAED,YAAY,CAAC,QAA6B;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IAED,SAAS,CAAC,QAA6B;QACtC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,QAA6B;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;CACD;AAED,MAAqB,4BAA4B;IAIhD,YAA6B,iBAAoC;QAApC,sBAAiB,GAAjB,iBAAiB,CAAmB;QAHzD,UAAK,GAA0B,IAAI,GAAG,EAAE,CAAC;QACzC,oBAAe,GAAW,CAAC,CAAC;QAkEnB,+BAA0B,GAAG,IAAI,GAAG,EAAU,CAAC;IAhEK,CAAC;IAEtE,YAAY,CAAC,QAA6B,EAAE,MAAc;QACzD,qBAAqB;QAErB,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,GAAG,EAAE,CAAC;YACV,yDAAyD;YACzD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,EAAE,CAAC;YAChB,SAAS,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YACvD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAChC,CAAC;aACI,CAAC;YACL,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE;YACvC,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,SAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YAErF,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,GAAI,CAAC,CAAC;YAEzB,OAAO,SAAS,CAAC;QAClB,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,SAAS,CAAC,QAA6B,EAAE,MAAc;QACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED,aAAa,CAAC,MAAc;QAC3B,OAAO,IAAI,kCAAkC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7D,CAAC;IAED,MAAM,CAAC,QAA6B;QACnC,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEvC,IAAI,GAAG,EAAE,CAAC;YACT,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;IACF,CAAC;IAED,OAAO;QACN,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;IAIO,mBAAmB,CAAC,QAA6B,EAAE,QAAkB;QAC5E,MAAM,gBAAgB,GAAG,yCAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAExE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACvB,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,SAAS,GAAG,yCAAmB,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAErF,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QACpC,iGAAiG;QACjG,+DAA+D;QAC/D,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEzC;;;;;;cAME;YACF,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,gDAAgD,EAAE,EAAE,EAAE;gBAC/F,aAAa,EAAE,SAAS,CAAC,MAAM;aAC/B,CAAC,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,WAAW,CAAC,QAA6B;QAChD,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;YAClB,OAAO,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAChC,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;CACD;AA3GD,+CA2GC", "file": "documentTracker.js", "sourceRoot": "../src/"}