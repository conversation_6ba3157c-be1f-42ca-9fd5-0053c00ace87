{"version": 3, "sources": ["typingsInstaller/typingsInstaller.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG;;;;;;;;;;;;;;;;;;GAkBG;AAEH,mEAAyE;AACzE,+BAA4B;AAC5B,mEAAqD;AACrD,yCAA6E;AAI7E;;;GAGG;AACH,MAAa,yBAAyB;IAUrC,YACkB,EAAwB,EAChC,0BAAkC;QAD1B,OAAE,GAAF,EAAE,CAAsB;QAChC,+BAA0B,GAA1B,0BAA0B,CAAQ;QARpC,sBAAiB,GAAG,KAAK,CAAC;QAE1B,uBAAkB,GAAoC,IAAI,GAAG,EAAE,CAAC;QAQvE,IAAI,CAAC,MAAM,GAAG,yBAAyB,CAAC,UAAU,CACjD,CAAC,QAA2B,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC9D,IAAI,CAAC,EAAE,EACP,0BAA0B,CAC1B,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc,CAAC,QAA2B;QACvD,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,0BAA0B,CAAC;YAChC,KAAK,oBAAoB,CAAC;YAC1B,KAAK,aAAa;gBACjB,IAAI,CAAC,cAAe,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;gBACvD,MAAM;YACP,KAAK,0BAA0B,CAAC;YAChC,KAAK,wBAAwB,CAAC;YAC9B,6CAA6C;YAC7C,KAAK,8BAA8B;gBAClC,cAAc;gBACd,MAAM;YACP;gBACC,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACtE,CAAC;IACF,CAAC;IAED,0EAA0E;IAC1E,uDAAuD;IACvD,KAAK,CAAC,cAAc,CAAC,QAAoD;QACxE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAED,iEAAiE;IACjE,eAAe;IACf,uBAAuB,CAAC,WAAmB;QAC1C,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,kBAAkB,GAAG,IAAA,oCAAyB,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,kBAAkB,CAAC,MAAM,oCAA4B,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACd,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;QACjE,OAAO,KAAK,CAAC;IACd,CAAC;IAED,4BAA4B,CAAC,CAAoB,EAAE,eAAmC,EAAE,iBAAiD;QACxI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;QAChF,MAAM,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;QACzF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,cAAwC;QAC9C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACtC,CAAC;IAED,eAAe,CAAC,eAAkC;QACjD,OAAO;IACR,CAAC;CACD;AAlFD,8DAkFC;AAED;;;;;GAKG;AACH,MAAM,yBAA0B,SAAQ,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB;IAIlF,YACU,aAA8C,EACtC,cAAqD,EACtE,EAAwB,EACP,cAA8B,EAC/C,sBAA8B;QAE9B,KAAK,CAAC,EAAE,EAAE,sBAAsB,EAAE,IAAA,WAAI,EAAC,sBAAsB,EAAE,cAAc,CAAY,EAAE,IAAA,WAAI,EAAC,sBAAsB,EAAE,sBAAsB,CAAY,EAAE,QAAQ,CAAC,CAAC;QAN7J,kBAAa,GAAb,aAAa,CAAiC;QACtC,mBAAc,GAAd,cAAc,CAAuC;QAErD,mBAAc,GAAd,cAAc,CAAgB;IAIhD,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,UAAU,CACtB,cAAqD,EACrD,EAAwB,EACxB,sBAA8B;QAE9B,MAAM,EAAE,GAAG,IAAI,mCAAc,CAAC,EAAE,CAAC,CAAC;QAClC,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC;QAC7D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC3C,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,cAAc,CAAC,sBAAsB,EAAE;YAChE,WAAW,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC;SAC5C,CAAC,CAAC;QACH,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEzB,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA8B,CAAC;QACvD,MAAM,SAAS,GAAG,IAAA,WAAI,EAAC,sBAAsB,EAAE,wCAAwC,CAAC,CAAC;QACzF,MAAM,KAAK,GAAG,yBAAyB,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QAChE,KAAK,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YAClE,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,KAA2B,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,OAAO,IAAI,yBAAyB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,EAAE,sBAAsB,CAAC,CAAC;IAChG,CAAC;IAED;;;;;OAKG;IACgB,aAAa,CAAC,SAAiB,EAAE,YAAsB,EAAE,GAAW,EAAE,kBAAqE;QAC7J,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;QAC7C,CAAC,KAAK,IAAI,EAAE;YACX,IAAI,CAAC;gBACJ,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,GAAG,EAAE;oBAC9D,WAAW,EAAE,YAAY;oBACzB,WAAW,EAAE,gCAAW,CAAC,aAAa;iBACtC,CAAC,CAAC;gBACH,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACzB,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;QACF,CAAC,CAAC,EAAE,CAAC;IACN,CAAC;IAED;;;OAGG;IACgB,YAAY,CAAC,QAA2B;QAC1D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,QAAQ,CAAC,EAAwB,EAAE,IAAY;QAC7D,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAChC,CAAC;;AArFuB,kDAAwB,GAAG,gBAAgB,CAAC", "file": "typingsInstaller.js", "sourceRoot": "../../src/"}