{"version": 3, "sources": ["features/signatureHelpProvider.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,mCAA0I;AAC1I,yDAA2C;AAC3C,yEAA2D;AAE3D,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACjC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAE7B,MAAM,GAAG,GAAG,CAAC,CAAC;AAGd,MAAM,gBAAgB;IAMrB,YAAY,KAAmB,EAAE,MAAc,EAAE,UAAkB;QAClE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC/C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACpB,CAAC;IAEM,OAAO;QACb,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEM,IAAI;QACV,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;gBACpD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBACnC,OAAO,GAAG,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YACrB,OAAO,GAAG,CAAC;QACZ,CAAC;QACD,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,EAAE,CAAC;IACX,CAAC;CAED;AAGD,MAAqB,wBAAwB;IAErC,oBAAoB,CAAC,QAAsB,EAAE,QAAkB,EAAE,MAAyB;QAChG,MAAM,MAAM,GAAG,kBAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAU,eAAe,EAAE,IAAI,CAAC,CAAC;QACrF,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvF,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,KAAK,GAAG,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACtF,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACb,CAAC;QACD,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACxF,MAAM,aAAa,GAAG,IAAI,6BAAoB,CAAC,KAAK,GAAG,YAAY,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;QAExF,MAAM,EAAE,GAAG,2BAA2B,CAAC;QACvC,IAAI,KAAK,GAA2B,IAAI,CAAC;QACzC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACjD,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;QACvE,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,sBAAa,EAAE,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnC,GAAG,CAAC,eAAe,GAAG,CAAC,CAAC;QACxB,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAChF,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAEO,aAAa,CAAC,QAA0B;QAC/C,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,OAAO,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3B,MAAM,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC3B,QAAQ,EAAE,EAAE,CAAC;gBACZ,KAAK,QAAQ;oBACZ,aAAa,EAAE,CAAC;oBAChB,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;wBACvB,OAAO,UAAU,CAAC;oBACnB,CAAC;oBACD,MAAM;gBACP,KAAK,QAAQ;oBAAE,aAAa,EAAE,CAAC;oBAAC,MAAM;gBACtC,KAAK,OAAO;oBAAE,YAAY,EAAE,CAAC;oBAAC,MAAM;gBACpC,KAAK,OAAO;oBAAE,YAAY,EAAE,CAAC;oBAAC,MAAM;gBACpC,KAAK,SAAS;oBAAE,cAAc,EAAE,CAAC;oBAAC,MAAM;gBACxC,KAAK,SAAS;oBAAE,cAAc,EAAE,CAAC;oBAAC,MAAM;gBACxC,KAAK,OAAO,CAAC;gBACb,KAAK,MAAM;oBACV,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;wBACrD,yCAAyC;oBAC1C,CAAC;oBACD,MAAM;gBACP,KAAK,MAAM;oBACV,IAAI,CAAC,aAAa,IAAI,CAAC,cAAc,IAAI,CAAC,YAAY,EAAE,CAAC;wBACxD,UAAU,EAAE,CAAC;oBACd,CAAC;oBACD,MAAM;YACR,CAAC;QACF,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,WAAW,CAAC,EAAU;QAC7B,IAAI,EAAE,KAAK,IAAI,IAAI,IAAI;YACtB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,MAAM;YAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,MAAM;YAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,MAAM;YAC9B,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,EAAE,CAAC,CAAC,WAAW;YAEzC,OAAO,IAAI,CAAC;QACb,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEO,SAAS,CAAC,QAA0B;QAC3C,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,OAAO,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3B,MAAM,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;gBACjE,SAAS;YACV,CAAC;YACD,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC1B,YAAY,GAAG,IAAI,CAAC;gBACpB,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;YACzC,CAAC;iBAAM,IAAI,YAAY,EAAE,CAAC;gBACzB,OAAO,KAAK,CAAC;YACd,CAAC;QACF,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;CAED;AAxGD,2CAwGC", "file": "signatureHelpProvider.js", "sourceRoot": "../../src/"}