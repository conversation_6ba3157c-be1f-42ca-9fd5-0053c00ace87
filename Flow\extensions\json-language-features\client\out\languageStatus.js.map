{"version": 3, "sources": ["languageStatus.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAkKhG,4DAmDC;AAED,sDA+CC;AAKD,wEAQC;AAjRD,mCAIgB;AAahB,SAAS,8BAA8B;IACtC,MAAM,YAAY,GAAoE,EAAE,CAAC;IAEzF,KAAK,MAAM,SAAS,IAAI,mBAAU,CAAC,GAAG,EAAE,CAAC;QACxC,MAAM,eAAe,GAAG,SAAS,CAAC,WAAW,EAAE,WAAW,EAAE,cAAc,CAAC;QAC3E,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YACpC,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;gBAC9C,IAAI,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;gBAC7B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;oBAC7B,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;wBACtC,GAAG,GAAG,YAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACjE,CAAC;oBACD,YAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC3E,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO;QACN,aAAa,CAAC,GAAW;YACxB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACxC,IAAI,WAAW,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;oBACjC,OAAO;wBACN,KAAK,EAAE,WAAW,CAAC,KAAK;wBACxB,MAAM,EAAE,aAAI,CAAC,CAAC,CAAC,8BAA8B,EAAE,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;wBACxE,GAAG,EAAE,YAAG,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;wBACnC,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,kBAAS,CAAC,YAAY,CAAC,EAAE,OAAO,EAAE,aAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC;wBACvF,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,iBAAQ,CAAC,cAAc,CAAC,mDAAmD,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;qBAClI,CAAC;gBACH,CAAC;YACF,CAAC;YACD,OAAO,SAAS,CAAC;QAClB,CAAC;KACD,CAAC;AACH,CAAC;AAED,EAAE;AAEF,SAAS,6BAA6B,CAAC,GAAW;IACjD,MAAM,WAAW,GAAG,YAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnC,MAAM,eAAe,GAAG,kBAAS,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAElE,MAAM,QAAQ,GAAG,kBAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,OAAO,CAAuB,SAAS,CAAC,CAAC;IAE1G,MAAM,YAAY,GAAuF,EAAE,CAAC;IAE5G,MAAM,oBAAoB,GAAG,QAAQ,EAAE,oBAAoB,CAAC;IAC5D,IAAI,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC5D,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;YAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;YACxB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC7B,IAAI,OAAO,GAAG,GAAG,CAAC;gBAClB,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBACtC,OAAO,GAAG,YAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAClE,CAAC;gBACD,YAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YAC7D,CAAC;QACF,CAAC;IACF,CAAC;IACD,MAAM,kBAAkB,GAAG,QAAQ,EAAE,WAAW,CAAC;IACjD,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACvC,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;YAC1C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;YACxB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC7B,IAAI,OAAO,GAAG,GAAG,CAAC;gBAClB,IAAI,eAAe,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBACzD,OAAO,GAAG,YAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAClE,CAAC;gBACD,YAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YACxE,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO;QACN,WAAW,CAAC,GAAW;YACtB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACxC,IAAI,WAAW,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;oBACjC,OAAO;wBACN,KAAK,EAAE,WAAW,CAAC,KAAK;wBACxB,MAAM,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,aAAI,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,aAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC;wBACxH,GAAG,EAAE,YAAG,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;wBACnC,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,kBAAS,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,aAAI,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC;wBAChF,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,iBAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC,mCAAmC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;qBACnL,CAAC;gBACH,CAAC;YACF,CAAC;YACD,OAAO,SAAS,CAAC;QAClB,CAAC;KACD,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,KAAuB;IAE9C,MAAM,0BAA0B,GAAG,8BAA8B,EAAE,CAAC;IACpE,MAAM,yBAAyB,GAAG,6BAA6B,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE3E,MAAM,gBAAgB,GAAG,EAAE,CAAC;IAC5B,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,MAAM,YAAY,GAAG,EAAE,CAAC;IAExB,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QACvC,MAAM,cAAc,GAAG,0BAA0B,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAC3E,IAAI,cAAc,EAAE,CAAC;YACpB,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACtC,SAAS;QACV,CAAC;QACD,MAAM,aAAa,GAAG,yBAAyB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,aAAa,EAAE,CAAC;YACnB,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACpC,SAAS;QACV,CAAC;QACD,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,YAAG,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,KAAK,GAAsB,CAAC,GAAG,gBAAgB,EAAE,GAAG,eAAe,EAAE,GAAG,YAAY,CAAC,CAAC;IAC5F,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,KAAK,CAAC,IAAI,CAAC;YACV,KAAK,EAAE,aAAI,CAAC,CAAC,CAAC,oCAAoC,CAAC;YACnD,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,kBAAS,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,aAAI,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC;YAChF,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,iBAAQ,CAAC,cAAc,CAAC,mCAAmC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;SACtG,CAAC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,0BAAiB,CAAC,SAAS,EAAE,CAAC,CAAC;IAC7D,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,aAAI,CAAC,CAAC,CAAC,+CAA+C,CAAC,EAAE,GAAG,EAAE,YAAG,CAAC,KAAK,CAAC,8EAA8E,CAAC,EAAE,CAAC,CAAC;IAE/K,MAAM,SAAS,GAAG,eAAM,CAAC,eAAe,EAAmB,CAAC;IAC5D,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,aAAI,CAAC,CAAC,CAAC,kCAAkC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACzG,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;IACxB,SAAS,CAAC,IAAI,EAAE,CAAC;IACjB,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE;QAC1B,MAAM,GAAG,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3C,IAAI,GAAG,EAAE,CAAC;YACT,iBAAQ,CAAC,cAAc,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;YAC5C,SAAS,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC;IACF,CAAC,CAAC,CAAC;IACH,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE;QACpC,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;YAChG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;QAChC,CAAC;IACF,CAAC,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,wBAAwB,CAAC,gBAAkC,EAAE,aAA2D;IACvI,MAAM,UAAU,GAAG,kBAAS,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;IAC9F,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC;IACnD,UAAU,CAAC,QAAQ,GAAG,+BAAsB,CAAC,WAAW,CAAC;IAEzD,MAAM,kBAAkB,GAAG,iBAAQ,CAAC,eAAe,CAAC,gCAAgC,EAAE,cAAc,CAAC,CAAC;IAEtG,MAAM,oBAAoB,GAAG,eAAM,CAAC,2BAA2B,CAAC,GAAG,EAAE;QACpE,oBAAoB,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,KAAK,UAAU,oBAAoB;QAClC,MAAM,QAAQ,GAAG,eAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;QACnD,IAAI,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC;gBACJ,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAC;gBACpC,UAAU,CAAC,MAAM,GAAG,aAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC;gBAChD,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;gBAE/B,MAAM,OAAO,GAAG,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;gBACvE,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC9B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC;oBACjD,UAAU,CAAC,MAAM,GAAG,aAAI,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC;gBACzD,CAAC;qBAAM,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACjC,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;oBAC7C,UAAU,CAAC,MAAM,GAAG,aAAI,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC;gBACtD,CAAC;qBAAM,CAAC;oBACP,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;oBAC7C,UAAU,CAAC,MAAM,GAAG,aAAI,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC;gBAChE,CAAC;gBACD,UAAU,CAAC,OAAO,GAAG;oBACpB,OAAO,EAAE,gCAAgC;oBACzC,KAAK,EAAE,aAAI,CAAC,CAAC,CAAC,cAAc,CAAC;oBAC7B,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAA6B,CAAC;iBACjF,CAAC;YACH,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,qCAAqC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;gBAC3E,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC;gBAC9B,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;YAChC,CAAC;QACF,CAAC;aAAM,CAAC;YACP,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC;YACxE,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC;YAC9B,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;QAChC,CAAC;IACF,CAAC;IAED,oBAAoB,EAAE,CAAC;IAEvB,OAAO,mBAAU,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;AAC9E,CAAC;AAED,SAAgB,qBAAqB,CAAC,OAAsC;IAC3E,IAAI,UAAkC,CAAC;IACvC,MAAM,YAAY,GAA8B,IAAI,GAAG,EAAE,CAAC;IAE1D,MAAM,SAAS,GAAiB,EAAE,CAAC;IACnC,SAAS,CAAC,IAAI,CAAC,eAAM,CAAC,2BAA2B,CAAC,UAAU,CAAC,EAAE;QAC9D,UAAU,EAAE,OAAO,EAAE,CAAC;QACtB,UAAU,GAAG,SAAS,CAAC;QACvB,MAAM,GAAG,GAAG,UAAU,EAAE,QAAQ,CAAC;QACjC,IAAI,GAAG,EAAE,CAAC;YACT,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACzB,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;QACF,CAAC;IACF,CAAC,CAAC,CAAC,CAAC;IACJ,SAAS,CAAC,IAAI,CAAC,kBAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE;QAC1D,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC;IAEJ,SAAS,MAAM,CAAC,QAAsB,EAAE,YAA4B;QACnE,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC5B,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC9B,IAAI,UAAU,IAAI,QAAQ,KAAK,eAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;gBAClE,UAAU,CAAC,OAAO,EAAE,CAAC;gBACrB,UAAU,GAAG,SAAS,CAAC;YACxB,CAAC;QACF,CAAC;aAAM,CAAC;YACP,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACzC,IAAI,QAAQ,KAAK,eAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;gBACpD,IAAI,CAAC,UAAU,IAAI,YAAY,KAAK,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChE,UAAU,EAAE,OAAO,EAAE,CAAC;oBACtB,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;gBACpC,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO;QACN,MAAM;QACN,OAAO;YACN,UAAU,EAAE,OAAO,EAAE,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACpC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACrB,UAAU,GAAG,SAAS,CAAC;YACvB,YAAY,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;KACD,CAAC;AACH,CAAC;AAED,MAAM,mBAAmB,GAAG,+BAA+B,CAAC;AAC5D,MAAM,sBAAsB,GAAG,aAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AAEnD,SAAgB,8BAA8B,CAAC,gBAAkC,EAAE,SAAiB,EAAE,KAAa;IAClH,MAAM,UAAU,GAAG,kBAAS,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,CAAC;IACtG,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;IAChD,UAAU,CAAC,QAAQ,GAAG,+BAAsB,CAAC,OAAO,CAAC;IACrD,UAAU,CAAC,IAAI,GAAG,aAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IACpC,UAAU,CAAC,MAAM,GAAG,aAAI,CAAC,CAAC,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;IAC7F,UAAU,CAAC,OAAO,GAAG,EAAE,OAAO,EAAE,mBAAmB,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;IAC7G,OAAO,mBAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACpC,CAAC", "file": "languageStatus.js", "sourceRoot": "../src/"}