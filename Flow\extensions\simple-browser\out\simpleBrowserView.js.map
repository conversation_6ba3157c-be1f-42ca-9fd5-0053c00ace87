{"version": 3, "sources": ["simpleBrowserView.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,uCAAuC;AAQvC,MAAa,iBAAkB,SAAQ,oBAAU;IAKxC,MAAM,CAAC,4BAA4B,CAAC,YAAwB;QACnE,OAAO;YACN,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;SAC1C,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,YAAwB;QACxD,OAAO;YACN,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;YACjB,kBAAkB,EAAE,iBAAiB,CAAC,4BAA4B,CAAC,YAAY,CAAC;SAChF,CAAC;IACH,CAAC;IAOM,MAAM,CAAC,MAAM,CACnB,YAAwB,EACxB,GAAW,EACX,WAAyB;QAEzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,KAAK,EAAE;YACrG,UAAU,EAAE,WAAW,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM;YAC/D,aAAa,EAAE,WAAW,EAAE,aAAa;SACzC,EAAE;YACF,uBAAuB,EAAE,IAAI;YAC7B,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC;SACpD,CAAC,CAAC;QACH,OAAO,IAAI,iBAAiB,CAAC,YAAY,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAEM,MAAM,CAAC,OAAO,CACpB,YAAwB,EACxB,GAAW,EACX,YAAiC;QAEjC,OAAO,IAAI,iBAAiB,CAAC,YAAY,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;IAC/D,CAAC;IAED,YACkB,YAAwB,EACzC,GAAW,EACX,YAAiC;QAEjC,KAAK,EAAE,CAAC;QAJS,iBAAY,GAAZ,YAAY,CAAY;QA3BzB,kBAAa,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC,CAAC;QACjE,cAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;QAgCpD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEvF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;YACjE,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;gBAChB,KAAK,cAAc;oBAClB,IAAI,CAAC;wBACJ,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBACpC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;oBAC9B,CAAC;oBAAC,MAAM,CAAC;wBACR,OAAO;oBACR,CAAC;oBACD,MAAM;YACR,CAAC;QACF,CAAC,CAAC,CAAC,CAAC;QAEJ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,GAAG,EAAE;YACnD,IAAI,CAAC,OAAO,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC;QAEJ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5D,IAAI,CAAC,CAAC,oBAAoB,CAAC,0CAA0C,CAAC,EAAE,CAAC;gBACxE,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;gBACzE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC;oBACtC,IAAI,EAAE,oCAAoC;oBAC1C,gBAAgB,EAAE,aAAa,CAAC,GAAG,CAAU,4BAA4B,EAAE,IAAI,CAAC;iBAChF,CAAC,CAAC;YACJ,CAAC;QACF,CAAC,CAAC,CAAC,CAAC;QAEJ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChB,CAAC;IAEe,OAAO;QACtB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC1B,KAAK,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAEM,IAAI,CAAC,GAAW,EAAE,OAAqB;QAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IACxE,CAAC;IAEO,OAAO,CAAC,GAAW;QAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAEzE,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAEtE,OAAO,UAAU,CAAC;;;;;;;;iBAQH,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS;yBAC5B,KAAK;;;;wDAI0B,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC;YACpF,GAAG,EAAE,GAAG;YACR,gBAAgB,EAAE,aAAa,CAAC,GAAG,CAAU,4BAA4B,EAAE,IAAI,CAAC;SAChF,CAAC,CAAC;;mDAE8C,OAAO;mDACP,WAAW;;;;;;gBAM9C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;;;;gBAIrB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;;;;gBAIxB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;;;;;;;;gBAQvB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC;;;;;yCAKP,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC;;;;mBAIjD,MAAM,YAAY,KAAK;;WAE/B,CAAC;IACX,CAAC;IAEO,oBAAoB,CAAC,GAAG,KAAe;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;IAClG,CAAC;;AAnKF,8CAoKC;AAlKuB,0BAAQ,GAAG,oBAAH,AAAuB,CAAC;AAC/B,uBAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAjC,AAAkC,CAAC;AAmKjE,SAAS,eAAe,CAAC,KAA0B;IAClD,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,QAAQ;IAChB,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;IAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IACtE,CAAC;IACD,OAAO,IAAI,CAAC;AACb,CAAC", "file": "simpleBrowserView.js", "sourceRoot": "../src/"}