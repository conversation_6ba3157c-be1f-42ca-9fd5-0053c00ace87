{"version": 3, "sources": ["mergeConflictParser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;gGAGgG;AAChG,+CAAiC;AAEjC,mEAAgE;AAGhE,MAAM,iBAAiB,GAAG,SAAS,CAAC;AACpC,MAAM,qBAAqB,GAAG,SAAS,CAAC;AACxC,MAAM,cAAc,GAAG,SAAS,CAAC;AACjC,MAAM,eAAe,GAAG,SAAS,CAAC;AASlC,MAAa,mBAAmB;IAE/B,MAAM,CAAC,YAAY,CAAC,QAA6B,EAAE,iBAAoC;QAEtF,kFAAkF;QAClF,kFAAkF;QAClF,qFAAqF;QACrF,oFAAoF;QACpF,UAAU;QAEV,IAAI,eAAe,GAA+B,IAAI,CAAC;QACvD,MAAM,mBAAmB,GAAkD,EAAE,CAAC;QAE9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAEhC,qBAAqB;YACrB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvC,SAAS;YACV,CAAC;YAED,gCAAgC;YAChC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC7C,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;oBAC9B,wEAAwE;oBACxE,eAAe,GAAG,IAAI,CAAC;oBAEvB,4EAA4E;oBAC5E,0BAA0B;oBAC1B,MAAM;gBACP,CAAC;gBAED,8CAA8C;gBAC9C,eAAe,GAAG,EAAE,WAAW,EAAE,IAAI,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;YAC9D,CAAC;YACD,gFAAgF;iBAC3E,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBACtG,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC;YACD,iEAAiE;iBAC5D,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBACvF,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC;YACjC,CAAC;YACD,+DAA+D;iBAC1D,IAAI,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnE,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC;gBAEjC,2EAA2E;gBAC3E,iDAAiD;gBACjD,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,kCAAkC,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;gBAE7G,IAAI,kBAAkB,KAAK,IAAI,EAAE,CAAC;oBACjC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC9C,CAAC;gBAED,mEAAmE;gBACnE,0BAA0B;gBAC1B,eAAe,GAAG,IAAI,CAAC;YACxB,CAAC;QACF,CAAC;QAED,OAAO,mBAAmB;aACxB,MAAM,CAAC,OAAO,CAAC;aACf,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,6CAAqB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAC/E,CAAC;IAEO,MAAM,CAAC,kCAAkC,CAAC,QAA6B,EAAE,OAA4B;QAC5G,gEAAgE;QAChE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACrE,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,sBAAsB,GAAoB,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC;QAE/F,4FAA4F;QAC5F,wEAAwE;QACxE,qGAAqG;QACrG,sGAAsG;QACtG,4CAA4C;QAC5C,OAAO;YACN,OAAO,EAAE;gBACR,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK;gBACjC,gBAAgB,EAAE,IAAI,MAAM,CAAC,KAAK,CACjC,OAAO,CAAC,WAAW,CAAC,uBAAuB,CAAC,GAAG,EAC/C,mBAAmB,CAAC,qBAAqB,CAAC,QAAQ,EAAE,sBAAsB,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;gBAC1I,8GAA8G;gBAC9G,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CACxB,OAAO,CAAC,WAAW,CAAC,uBAAuB,CAAC,GAAG,EAC/C,sBAAsB,CAAC,KAAK,CAAC,KAAK,CAAC;gBACpC,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;aACtE;YACD,eAAe,EAAE,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE;gBACzF,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC;gBACrE,OAAO;oBACN,MAAM,EAAE,gBAAgB,CAAC,KAAK;oBAC9B,gBAAgB,EAAE,IAAI,MAAM,CAAC,KAAK,CACjC,gBAAgB,CAAC,uBAAuB,CAAC,GAAG,EAC5C,mBAAmB,CAAC,qBAAqB,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;oBAC9H,0EAA0E;oBAC1E,+EAA+E;oBAC/E,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CACxB,gBAAgB,CAAC,uBAAuB,CAAC,GAAG,EAC5C,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC;oBAC3B,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;iBACvE,CAAC;YACH,CAAC,CAAC;YACF,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK;YAChC,QAAQ,EAAE;gBACT,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK;gBAC/B,gBAAgB,EAAE,IAAI,MAAM,CAAC,KAAK,CACjC,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,GAAG,EAC5C,mBAAmB,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;gBAClI,sFAAsF;gBACtF,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CACxB,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,GAAG,EAC5C,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC/B,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;aAClE;YACD,8FAA8F;YAC9F,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,uBAAuB,CAAC,GAAG,CAAC;SACvG,CAAC;IACH,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,QAA6B;QACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;IAC3E,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,QAA6B,EAAE,KAAsB,EAAE,WAA4B;QACvH,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACd,CAAC;QAED,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACtB,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;QAEpC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YACnB,IAAI,EAAE,CAAC;YACP,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;CACD;AAnJD,kDAmJC", "file": "mergeConflictParser.js", "sourceRoot": "../src/"}