{"version": 3, "sources": ["dropOrPaste/dropOrPasteResource.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkIhG,gFAmBC;AAnJD,2CAA6B;AAC7B,+CAAiC;AACjC,qCAA0D;AAC1D,uCAAoC;AAEpC,MAAM,2BAA2B;IAAjC;QAEU,SAAI,GAAG,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAuGvF,CAAC;IArGA,KAAK,CAAC,wBAAwB,CAC7B,QAA6B,EAC7B,QAAyB,EACzB,YAAiC,EACjC,KAA+B;QAE/B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAC9D,OAAO;QACR,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAC/C,OAAO;QACR,CAAC;QAED,OAAO;YACN,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;YACjC,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACxH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC9B,QAA6B,EAC7B,MAA+B,EAC/B,YAAiC,EACjC,QAAyC,EACzC,KAA+B;QAE/B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAC9D,OAAO;QACR,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAC/C,OAAO;QACR,CAAC;QAED,OAAO,CAAC;gBACP,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;gBACjC,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC/H,CAAC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,YAAiC;QACzD,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,cAAK,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC;QAClE,IAAI,OAAO,EAAE,CAAC;YACb,OAAO,iBAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;QAED,oBAAoB;QACpB,MAAM,IAAI,GAAiB,EAAE,CAAC;QAC9B,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,YAAY,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC;gBACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC;QACF,CAAC;QAED,OAAO,IAAI,iBAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAkB,EAAE,OAAgB;QACtE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7B,OAAO;QACR,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,YAAY,GAAG,eAAe,CAAC,IAAA,uBAAc,EAAC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC;YAExC,OAAO,CAAC,UAAU,CAAC,OAAO,OAAO,GAAG,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACF,CAAC;QAED,OAAO;YACN,OAAO;YACP,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;gBAChC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB,CAAC;gBACzC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC;SACzC,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,QAA6B,EAAE,QAAyB;QACtF,MAAM,KAAK,GAAG,cAAc,CAAC;QAC7B,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrF,IAAI,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,IAAI,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;gBAC5F,OAAO,KAAK,CAAC;YACd,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;CACD;AAED,SAAS,eAAe,CAAC,QAAgC,EAAE,MAAkB;IAC5E,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;QAC9F,IAAI,MAAM,CAAC,MAAM,KAAK,gBAAO,CAAC,IAAI,EAAE,CAAC;YACpC,mFAAmF;YACnF,iFAAiF;YACjF,4DAA4D;YAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,SAAS,CAAC;AAClB,CAAC;AAED,SAAgB,kCAAkC,CAAC,QAAiC;IACnF,MAAM,QAAQ,GAAG,IAAI,2BAA2B,EAAE,CAAC;IAEnD,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAC5B,MAAM,CAAC,SAAS,CAAC,gCAAgC,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACrE,qBAAqB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;QACtC,aAAa,EAAE;YACd,cAAK,CAAC,OAAO;YACb,OAAO;SACP;KACD,CAAC,EACF,MAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACtE,sBAAsB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvC,cAAc,EAAE;YACf,cAAK,CAAC,OAAO;YACb,OAAO;SACP;KACD,CAAC,CACF,CAAC;AACH,CAAC", "file": "dropOrPasteResource.js", "sourceRoot": "../../src/"}