{"version": 3, "sources": ["test/linkify.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,iCAA8B;AAC9B,wCAAmD;AAEnD,MAAM,GAAG,GAAG,IAAI,aAAK,EAAE,CAAC;AACxB,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;AAEtC,KAAK,CAAC,wCAAwC,EAAE,GAAG,EAAE;IAEpD,sBAAY,CAAC,mBAAmB,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,CAAC;IAE5D,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE;QACrB,MAAM,aAAa,GAAG,IAAA,iBAAO,EAAC,OAAO,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;QAC1F,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAC/B,MAAM,aAAa,GAAG,IAAA,iBAAO,EAAC,qCAAqC,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;QACxH,MAAM,cAAc,GAAG,IAAA,iBAAO,EAAC,qCAAqC,EAAE,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;QAE3H,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,mEAAmE,CAAC,CAAC;QAC3G,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,CAAC;QAC/E,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,mEAAmE,CAAC,CAAC;QAC5G,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,EAAE,qCAAqC,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qBAAqB,EAAE,GAAG,EAAE;QAChC,MAAM,aAAa,GAAG,IAAA,iBAAO,EAAC,wDAAwD,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;QAC3I,MAAM,cAAc,GAAG,IAAA,iBAAO,EAAC,wDAAwD,EAAE,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;QAE7I,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,qEAAqE,CAAC,CAAC;QAC7G,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAC;QACpE,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,qEAAqE,CAAC,CAAC;QAC9G,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACpC,MAAM,aAAa,GAAG,IAAA,iBAAO,EAAC,gDAAgD,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;QAEpI,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,4DAA4D,CAAC,CAAC;QACpG,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,gDAAgD,CAAC,CAAC;IAC3F,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "linkify.test.js", "sourceRoot": "../../src/"}