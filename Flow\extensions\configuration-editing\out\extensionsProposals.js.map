{"version": 3, "sources": ["extensionsProposals.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKhG,gFAuBC;AA1BD,+CAAiC;AAG1B,KAAK,UAAU,kCAAkC,CAAC,QAAkB,EAAE,cAAsB,EAAE,KAAmB,EAAE,wBAAiC;IAC1J,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAG,wBAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,2BAA2B,CAAC,CAAC,CAAC;QAC/K,MAAM,uBAAuB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtF,IAAI,uBAAuB,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACtC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,cAAc,EAAE,CAAC;gBAChD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;gBAC5C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACb,CAAC,CAAC,CAAC;QACJ,CAAC;aAAM,CAAC;YACP,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACpE,OAAO,CAAC,UAAU,GAAG,iBAAiB,CAAC;YACvC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC/C,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;YACtB,OAAO,CAAC,OAAO,CAAC,CAAC;QAClB,CAAC;IACF,CAAC;IACD,OAAO,EAAE,CAAC;AACX,CAAC", "file": "extensionsProposals.js", "sourceRoot": "../src/"}