{"version": 3, "sources": ["extensionEngineValidation.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AA8BhG,8CAGC;AAED,oCAoCC;AAED,4CAwCC;AAtFD,MAAM,cAAc,GAAG,kDAAkD,CAAC;AAC1E,MAAM,iBAAiB,GAAG,0BAA0B,CAAC;AAErD,SAAgB,iBAAiB,CAAC,OAAe;IAChD,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IACzB,OAAO,CAAC,OAAO,KAAK,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,SAAgB,YAAY,CAAC,OAAe;IAC3C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC;IACb,CAAC;IAED,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAEzB,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;QACrB,OAAO;YACN,QAAQ,EAAE,KAAK;YACf,gBAAgB,EAAE,KAAK;YACvB,SAAS,EAAE,CAAC;YACZ,cAAc,EAAE,KAAK;YACrB,SAAS,EAAE,CAAC;YACZ,cAAc,EAAE,KAAK;YACrB,SAAS,EAAE,CAAC;YACZ,cAAc,EAAE,KAAK;YACrB,UAAU,EAAE,IAAI;SAChB,CAAC;IACH,CAAC;IAED,MAAM,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,CAAC,EAAE,CAAC;QACR,OAAO,IAAI,CAAC;IACb,CAAC;IACD,OAAO;QACN,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;QACtB,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;QAC/B,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAChD,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7C,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAChD,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7C,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAChD,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7C,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;KACxB,CAAC;AACH,CAAC;AAED,SAAgB,gBAAgB,CAAC,OAA8B;IAC9D,IAAI,CAAC,OAAO,EAAE,CAAC;QACd,OAAO,IAAI,CAAC;IACb,CAAC;IAED,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC9C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,IAAI,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,IAAI,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAE5C,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACrB,cAAc,GAAG,KAAK,CAAC;QACxB,CAAC;aAAM,CAAC;YACP,cAAc,GAAG,KAAK,CAAC;YACvB,cAAc,GAAG,KAAK,CAAC;QACxB,CAAC;IACF,CAAC;IAED,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;QACxB,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACzD,IAAI,KAAK,EAAE,CAAC;YACX,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;YACnC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACpE,CAAC;IACF,CAAC;IAED,OAAO;QACN,SAAS,EAAE,SAAS;QACpB,cAAc,EAAE,cAAc;QAC9B,SAAS,EAAE,SAAS;QACpB,cAAc,EAAE,cAAc;QAC9B,SAAS,EAAE,SAAS;QACpB,cAAc,EAAE,cAAc;QAC9B,SAAS,EAAE,OAAO,CAAC,gBAAgB;QACnC,SAAS;KACT,CAAC;AACH,CAAC", "file": "extensionEngineValidation.js", "sourceRoot": "../src/"}