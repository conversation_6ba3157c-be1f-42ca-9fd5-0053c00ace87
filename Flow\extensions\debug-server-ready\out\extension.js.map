{"version": 3, "sources": ["extension.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+ThG,4BA4BC;AAzVD,+CAAiC;AACjC,2CAA6B;AAC7B,mCAAoC;AAEpC,MAAM,OAAO,GAAG,uCAAuC,CAAC,CAAC,iFAAiF;AAC1I,MAAM,eAAe,GAAG,qBAAqB,CAAC;AAC9C,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,QAAQ,GAAG,oBAAoB,CAAC;AAYtC,qCAAqC;AACrC,MAAM,YAAY,GAAG,uDAAuD,CAAC;AAC7E,MAAM,YAAY,GAAG,wCAAwC,CAAC;AAC9D,MAAM,YAAY,GAAG,8CAA8C,CAAC;AACpE,MAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG;IAC5C,YAAY,CAAC,MAAM;IACnB,YAAY,CAAC,MAAM;IACnB,YAAY,CAAC,MAAM;CACnB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AAExB;;;GAGG;AACH,SAAS,qBAAqB,CAAC,GAAW;IACzC,IAAI,GAAG,EAAE,CAAC;QACT,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,OAAO,GAAG,CAAC;AACZ,CAAC;AAED,MAAM,OAAO;IAAb;QACS,WAAM,GAAG,KAAK,CAAC;IASxB,CAAC;IAPA,IAAW,QAAQ;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAEM,IAAI;QACV,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,CAAC;CACD;AAED,MAAM,mBAAoB,SAAQ,MAAM,CAAC,UAAU;IAYlD,MAAM,CAAC,KAAK,CAAC,OAA4B;QACxC,IAAI,OAAO,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YAC7C,IAAI,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACf,QAAQ,GAAG,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAC5C,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACtD,CAAC;YACD,OAAO,QAAQ,CAAC;QACjB,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,OAA4B;QACvC,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,QAAQ,EAAE,CAAC;YACd,mBAAmB,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC9C,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC1B,QAAQ,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC;IACF,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,OAA4B,EAAE,GAAW;QAChE,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,QAAQ,EAAE,CAAC;YACd,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;QACzB,CAAC;IACF,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,0BAA0B;QACtC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAChC,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAC,CAAC,EAAC,EAAE;gBAE1E,8CAA8C;gBAC9C,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACvC,MAAM,GAAG,GAAG,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC1C,KAAK,MAAM,CAAC,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC3C,IAAI,QAAQ,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;wBAC/B,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;wBAC5B,OAAO;oBACR,CAAC;gBACF,CAAC;gBAED,qDAAqD;gBACrD,KAAK,MAAM,CAAC,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC3C,IAAI,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC;wBACjC,OAAO;oBACR,CAAC;gBACF,CAAC;YACF,CAAC,CAAC,CAAC;QACJ,CAAC;IACF,CAAC;IAED,YAA4B,OAA4B;QACvD,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QADT,YAAO,GAAP,OAAO,CAAqB;QA3DvC,mBAAc,GAAG,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;QACjD,qBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;QAC7C,gBAAW,GAAG,IAAI,GAAG,CAAoB,EAAE,CAAC,CAAC;QA4D7D,6DAA6D;QAC7D,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,OAAO,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3F,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,OAAO,IAAI,OAAO,EAAE,GAAG,CAAC,CAAC;IAC3F,CAAC;IAEO,eAAe;QACtB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAEM,cAAc;QACpB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;IAC5B,CAAC;IAED,aAAa,CAAC,CAAS;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChF,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC;YACb,CAAC;QACF,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEO,sBAAsB,CAAC,OAA4B,EAAE,aAAqB;QACjF,MAAM,IAAI,GAAsB,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAExE,IAAI,GAAG,CAAC;QACR,IAAI,aAAa,KAAK,EAAE,EAAE,CAAC;YAC1B,0FAA0F;YAC1F,2CAA2C;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;YACpC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,0FAA0F,EAAE,MAAM,CAAC,CAAC;gBACjI,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC7E,OAAO;YACR,CAAC;YACD,GAAG,GAAG,MAAM,CAAC;QACd,CAAC;aAAM,CAAC;YACP,uFAAuF;YACvF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YACjG,iDAAiD;YACjD,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7B,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpB,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uEAAuE,EAAE,MAAM,CAAC,CAAC;gBAC9G,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC7E,OAAO;YACR,CAAC;YACD,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAA4B,EAAE,GAAW;QAE1E,MAAM,IAAI,GAAsB,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC;QACxE,QAAQ,IAAI,CAAC,MAAM,IAAI,gBAAgB,EAAE,CAAC;YAEzC,KAAK,gBAAgB;gBACpB,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBACrD,MAAM;YAEP,KAAK,iBAAiB;gBACrB,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;gBACxD,MAAM;YAEP,KAAK,eAAe;gBACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;gBACxD,MAAM;YAEP,KAAK,gBAAgB;gBACpB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtE,CAAC;qBAAM,CAAC;oBACP,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI,aAAa,CAAC,CAAC;gBACnE,CAAC;gBACD,MAAM;YAEP;gBACC,gBAAgB;gBAChB,MAAM;QACR,CAAC;IACF,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,OAA4B,EAAE,GAAW;QACrF,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,iBAAsC,CAAC;QAC1E,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;YACxD,OAAO;QACR,CAAC;QAED,MAAM,SAAS,GAAG,IAAA,mBAAU,GAAE,CAAC;QAC/B,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACjD,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,0BAA0B,KAAK,SAAS,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAE9I,IAAI,CAAC,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC;YACzE,GAAG,CAAC,MAAM,EAAE,CAAC;YACb,GAAG,CAAC,OAAO,EAAE,CAAC;YACd,OAAO;QACR,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,iBAAiB,CAAC;QAC/C,GAAG,CAAC,OAAO,EAAE,CAAC;QAEd,IAAI,CAAC,cAAc,EAAE,CAAC;YACrB,OAAO;QACR,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;YACrD,YAAY,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAEO,wBAAwB,CAAC,IAAY,EAAE,OAA4B,EAAE,GAAW,EAAE,SAAkB;QAC3G,OAAO,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,EAAE;YAC3D,IAAI;YACJ,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,QAAQ;YACjB,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,OAAO,IAAI,QAAQ;YACpE,0BAA0B,EAAE,SAAS;SACrC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,iBAAiB,CAAC,OAA4B,EAAE,IAAY,EAAE,MAAkC;QAC7G,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,iBAAsC,CAAC;QAC1E,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5B,MAAM,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;YAC3E,OAAO;QACR,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACjD,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAEzF,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC;YACjF,GAAG,CAAC,MAAM,EAAE,CAAC;YACb,GAAG,CAAC,OAAO,EAAE,CAAC;YACd,OAAO;QACR,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,iBAAiB,CAAC;QAC/C,GAAG,CAAC,OAAO,EAAE,CAAC;QAEd,IAAI,CAAC,cAAc,EAAE,CAAC;YACrB,OAAO;QACR,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;YACrD,YAAY,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACtC,MAAM,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAEO,wBAAwB,CAAC,SAAoD,EAAE,iBAA2C;QACjI,OAAO,IAAI,OAAO,CAAkC,QAAQ,CAAC,EAAE;YAC9D,MAAM,IAAI,GAAG,CAAC,KAA2B,EAAE,EAAE;gBAC5C,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACnB,oBAAoB,CAAC,OAAO,EAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;gBAC9C,QAAQ,CAAC,KAAK,CAAC,CAAC;YACjB,CAAC,CAAC;YAEF,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC7E,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE;gBAC9D,IAAI,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxB,IAAI,CAAC,OAAO,CAAC,CAAC;gBACf,CAAC;YACF,CAAC,CAAC,CAAC;YAEH,iEAAiE;YACjE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACJ,CAAC;;AAnQc,6BAAS,GAAG,IAAI,GAAG,EAAV,AAAsD,CAAC;AAsQhF,SAAgB,QAAQ,CAAC,OAAgC;IAExD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE;QACxE,IAAI,OAAO,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,QAAQ,EAAE,CAAC;gBACd,mBAAmB,CAAC,0BAA0B,EAAE,CAAC;YAClD,CAAC;QACF,CAAC;IACF,CAAC,CAAC,CAAC,CAAC;IAEJ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,EAAE;QAC5E,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC;IAEJ,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;IAEnC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,GAAG,EAAE;QAC/E,iDAAiD,CAAC,OAA2C,EAAE,kBAA6C;YAC3I,IAAI,kBAAkB,CAAC,IAAI,IAAI,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;gBACrE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5C,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBACtC,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACvD,CAAC;YACF,CAAC;YACD,OAAO,kBAAkB,CAAC;QAC3B,CAAC;KACD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAgC,EAAE,IAAY;IAE1E,+CAA+C;IAC/C,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,IAAI,EAAE;QAChF,yBAAyB,CAAC,OAA4B;YACrD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,QAAQ,EAAE,CAAC;gBACd,IAAI,uBAA2C,CAAC;gBAChD,OAAO;oBACN,gBAAgB,EAAE,CAAC,CAAC,EAAE;wBACrB,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;4BAC1D,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gCACzB,KAAK,SAAS,CAAC;gCACf,KAAK,QAAQ,CAAC;gCACd,KAAK,QAAQ;oCACZ,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;wCACnB,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oCACvC,CAAC;oCACD,MAAM;gCACP;oCACC,MAAM;4BACR,CAAC;wBACF,CAAC;wBACD,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK,eAAe,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;4BAC1E,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gCACvC,uBAAuB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,0CAA0C;4BAC5E,CAAC;wBACF,CAAC;oBACF,CAAC;oBACD,oBAAoB,EAAE,CAAC,CAAC,EAAE;wBACzB,IAAI,uBAAuB,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,OAAO,KAAK,eAAe,IAAI,CAAC,CAAC,IAAI,IAAI,uBAAuB,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;4BAC9I,uBAAuB,GAAG,SAAS,CAAC;4BACpC,mBAAmB,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACtE,CAAC;oBACF,CAAC;iBACD,CAAC;YACH,CAAC;YACD,OAAO,SAAS,CAAC;QAClB,CAAC;KACD,CAAC,CAAC,CAAC;AACL,CAAC", "file": "extension.js", "sourceRoot": "../src/"}