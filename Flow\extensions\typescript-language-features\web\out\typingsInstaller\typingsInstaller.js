"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebTypingsInstallerClient = void 0;
/*
 * This file implements the global typings installer API for web clients. It
 * uses [nassun](https://docs.rs/nassun) and
 * [node-maintainer](https://docs.rs/node-maintainer) to install typings
 * in-memory (and maybe eventually cache them in IndexedDB?).
 *
 * Implementing a typings installer involves implementing two parts:
 *
 * -> ITypingsInstaller: the "top level" interface that tsserver uses to
 * request typings. Implementers of this interface are what actually get
 * passed to tsserver.
 *
 * -> TypingsInstaller: an abstract class that implements a good chunk of
 * the "generic" functionality for what ITypingsInstaller needs to do. For
 * implementation detail reasons, it does this in a "server/client" model of
 * sorts. In our case, we don't need a separate process, or even _quite_ a
 * pure "server/client" model, so we play along a bit for the sake of reusing
 * the stuff the abstract class is already doing for us.
 */
const ts_package_manager_1 = require("@vscode/ts-package-manager");
const path_1 = require("path");
const ts = __importStar(require("typescript/lib/tsserverlibrary"));
const jsTyping_1 = require("./jsTyping");
/**
 * The "server" part of the "server/client" model. This is the part that
 * actually gets instantiated and passed to tsserver.
 */
class WebTypingsInstallerClient {
    constructor(fs, globalTypingsCacheLocation) {
        this.fs = fs;
        this.globalTypingsCacheLocation = globalTypingsCacheLocation;
        this.requestedRegistry = false;
        this.typesRegistryCache = new Map();
        this.server = WebTypingsInstallerServer.initialize((response) => this.handleResponse(response), this.fs, globalTypingsCacheLocation);
    }
    /**
     * TypingsInstaller expects a "server/client" model, and as such, some of
     * its methods are implemented in terms of sending responses back to a
     * client. This method is a catch-all for those responses generated by
     * TypingsInstaller internals.
     */
    async handleResponse(response) {
        switch (response.kind) {
            case 'action::packageInstalled':
            case 'action::invalidate':
            case 'action::set':
                this.projectService.updateTypingsForProject(response);
                break;
            case 'event::beginInstallTypes':
            case 'event::endInstallTypes':
            // TODO(@zkat): maybe do something with this?
            case 'action::watchTypingLocations':
                // Don't care.
                break;
            default:
                throw new Error(`unexpected response: ${JSON.stringify(response)}`);
        }
    }
    // NB(kmarchan): this is a code action that expects an actual NPM-specific
    // installation. We shouldn't mess with this ourselves.
    async installPackage(_options) {
        throw new Error('not implemented');
    }
    // NB(kmarchan): As far as I can tell, this is only ever used for
    // completions?
    isKnownTypesPackageName(packageName) {
        console.log('isKnownTypesPackageName', packageName);
        const looksLikeValidName = (0, jsTyping_1.validatePackageNameWorker)(packageName, true);
        if (looksLikeValidName.result !== 0 /* NameValidationResult.Ok */) {
            return false;
        }
        if (this.requestedRegistry) {
            return !!this.typesRegistryCache && this.typesRegistryCache.has(packageName);
        }
        this.requestedRegistry = true;
        this.server.then(s => this.typesRegistryCache = s.typesRegistry);
        return false;
    }
    enqueueInstallTypingsRequest(p, typeAcquisition, unresolvedImports) {
        console.log('enqueueInstallTypingsRequest', typeAcquisition, unresolvedImports);
        const req = ts.server.createInstallTypingsRequest(p, typeAcquisition, unresolvedImports);
        this.server.then(s => s.install(req));
    }
    attach(projectService) {
        this.projectService = projectService;
    }
    onProjectClosed(_projectService) {
        // noop
    }
}
exports.WebTypingsInstallerClient = WebTypingsInstallerClient;
/**
 * Internal implementation of the "server" part of the "server/client" model.
 * This takes advantage of the existing TypingsInstaller to reuse a lot of
 * already-implemented logic around package installation, but with
 * installation details handled by Nassun/Node Maintainer.
 */
class WebTypingsInstallerServer extends ts.server.typingsInstaller.TypingsInstaller {
    constructor(typesRegistry, handleResponse, fs, packageManager, globalTypingsCachePath) {
        super(fs, globalTypingsCachePath, (0, path_1.join)(globalTypingsCachePath, 'fakeSafeList'), (0, path_1.join)(globalTypingsCachePath, 'fakeTypesMapLocation'), Infinity);
        this.typesRegistry = typesRegistry;
        this.handleResponse = handleResponse;
        this.packageManager = packageManager;
    }
    /**
     * Because loading the typesRegistry is an async operation for us, we need
     * to have a separate "constructor" that will be used by
     * WebTypingsInstallerClient.
     *
     * @returns a promise that resolves to a WebTypingsInstallerServer
     */
    static async initialize(handleResponse, fs, globalTypingsCachePath) {
        const pm = new ts_package_manager_1.PackageManager(fs);
        const pkgJson = (0, path_1.join)(globalTypingsCachePath, 'package.json');
        if (!fs.fileExists(pkgJson)) {
            fs.writeFile(pkgJson, '{"private":true}');
        }
        const resolved = await pm.resolveProject(globalTypingsCachePath, {
            addPackages: [this.typesRegistryPackageName]
        });
        await resolved.restore();
        const registry = new Map();
        const indexPath = (0, path_1.join)(globalTypingsCachePath, 'node_modules/types-registry/index.json');
        const index = WebTypingsInstallerServer.readJson(fs, indexPath);
        for (const [packageName, entry] of Object.entries(index.entries)) {
            registry.set(packageName, entry);
        }
        console.log('ATA registry loaded');
        return new WebTypingsInstallerServer(registry, handleResponse, fs, pm, globalTypingsCachePath);
    }
    /**
     * Implements the actual logic of installing a set of given packages. It
     * does this by looking up the latest versions of those packages using
     * Nassun, then handing Node Maintainer the updated package.json to run a
     * full install (modulo existing lockfiles, which can make this faster).
     */
    installWorker(requestId, packageNames, cwd, onRequestCompleted) {
        console.log('installWorker', requestId, cwd);
        (async () => {
            try {
                const resolved = await this.packageManager.resolveProject(cwd, {
                    addPackages: packageNames,
                    packageType: ts_package_manager_1.PackageType.DevDependency
                });
                await resolved.restore();
                onRequestCompleted(true);
            }
            catch (e) {
                onRequestCompleted(false);
            }
        })();
    }
    /**
     * This is a thing that TypingsInstaller uses internally to send
     * responses, and we'll need to handle this in the Client later.
     */
    sendResponse(response) {
        this.handleResponse(response);
    }
    /**
     * What it says on the tin. Reads a JSON file from the given path. Throws
     * if the file doesn't exist (as opposed to returning `undefined`, like
     * fs.readFile does).
     */
    static readJson(fs, path) {
        const data = fs.readFile(path);
        if (!data) {
            throw new Error('Failed to read file: ' + path);
        }
        return JSON.parse(data.trim());
    }
}
WebTypingsInstallerServer.typesRegistryPackageName = 'types-registry';
//# sourceMappingURL=typingsInstaller.js.map