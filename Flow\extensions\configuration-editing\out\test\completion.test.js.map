{"version": 3, "sources": ["test/completion.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,+CAAiC;AACjC,2BAAoC;AACpC,2CAA6B;AAC7B,uCAAyB;AACzB,iBAAe;AAGf,MAAM,UAAU,GAAG,aAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,eAAe,CAAC,CAAC,CAAC;AAEvE,KAAK,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC1C,MAAM,QAAQ,GAAG,eAAe,CAAC;IAEjC,IAAI,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QAC/B,CAAC,CAAC,uBAAuB;YACxB,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,6BAA6B;gBAC7B,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,gDAAgD;gBAChD,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,sBAAsB,EAAE,UAAU,EAAE,CAAC;YAC/D,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC,CAAC,8BAA8B;YAC/B,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2CAA2C;gBAC3C,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,uDAAuD;gBACvD,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,eAAe,EAAE,UAAU,EAAE,CAAC;YACxD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC,CAAC,6BAA6B;YAC9B,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2CAA2C;gBAC3C,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,uDAAuD;gBACvD,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,eAAe,EAAE,UAAU,EAAE,CAAC;YACxD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC,CAAC,wBAAwB;YACzB,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2CAA2C;gBAC3C,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,2CAA2C;gBAC3C,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,uBAAuB,EAAE,UAAU,EAAE,CAAC;YAChE,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC,CAAC,+BAA+B;YAChC,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,0BAA0B;gBAC1B,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,8BAA8B;gBAC9B,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YACnD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC,CAAC,sBAAsB;YACvB,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,qBAAqB;gBACrB,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,2CAA2C;gBAC3C,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,yBAAyB,EAAE,UAAU,EAAE,CAAC;YAClE,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC,CAAC,6BAA6B;YAC9B,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,8CAA8C;gBAC9C,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,uBAAuB,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;YACxE,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;QACrC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2BAA2B;gBAC3B,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,2BAA2B;gBAC3B,yCAAyC;gBACzC,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,sBAAsB,EAAE,UAAU,EAAE,CAAC;YAC/D,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2BAA2B;gBAC3B,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,2BAA2B;gBAC3B,4DAA4D;gBAC5D,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,iBAAiB,EAAE,UAAU,EAAE,CAAC;YAC1D,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2BAA2B;gBAC3B,2BAA2B;gBAC3B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,2BAA2B;gBAC3B,2BAA2B;gBAC3B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2BAA2B;gBAC3B,2BAA2B;gBAC3B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,2BAA2B;gBAC3B,2BAA2B;gBAC3B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,2BAA2B;gBAC3B,6BAA6B;gBAC7B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;YACzD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QAChC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,sBAAsB;gBACtB,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,sBAAsB;gBACtB,iCAAiC;gBACjC,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,oBAAoB,EAAE,UAAU,EAAE,CAAC;YAC7D,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,sBAAsB;gBACtB,6BAA6B;gBAC7B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,sBAAsB;gBACtB,gEAAgE;gBAChE,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,6BAA6B,EAAE,UAAU,EAAE,CAAC;YACtE,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACxC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,oCAAoC;gBACpC,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,oCAAoC;gBACpC,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;YAClD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,8BAA8B;gBAC9B,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,oCAAoC;gBACpC,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;YAClD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QACvC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,6BAA6B;gBAC7B,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;YACzC,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QACzC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,+BAA+B;gBAC/B,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;YACrC,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,KAAK,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC5C,MAAM,QAAQ,GAAG,iBAAiB,CAAC;IACnC,IAAI,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACxC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,wBAAwB;gBACxB,YAAY;gBACZ,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,wBAAwB;gBACxB,0BAA0B;gBAC1B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,oBAAoB,EAAE,UAAU,EAAE,CAAC;YAC7D,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;QACrC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,wBAAwB;gBACxB,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,wBAAwB;gBACxB,0BAA0B;gBAC1B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,oBAAoB,EAAE,UAAU,EAAE,CAAC;YAC7D,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,KAAK,CAAC,4BAA4B,EAAE,GAAG,EAAE;IACxC,MAAM,QAAQ,GAAG,aAAa,CAAC;IAC/B,IAAI,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QACvC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO;gBACP,gCAAgC;gBAChC,gCAAgC;gBAChC,+CAA+C;gBAC/C,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO;gBACP,gCAAgC;gBAChC,gCAAgC;gBAChC,iCAAiC;gBACjC,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO;gBACP,gCAAgC;gBAChC,gCAAgC;gBAChC,+CAA+C;gBAC/C,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO;gBACP,gCAAgC;gBAChC,gCAAgC;gBAChC,oDAAoD;gBACpD,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO;gBACP,wBAAwB;gBACxB,iCAAiC;gBACjC,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO;gBACP,wBAAwB;gBACxB,2BAA2B;gBAC3B,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,KAAK,CAAC,2BAA2B,EAAE,GAAG,EAAE;IACvC,MAAM,QAAQ,GAAG,YAAY,CAAC;IAC9B,IAAI,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QACvC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,uBAAuB;gBACvB,cAAc;gBACd,OAAO;gBACP,wBAAwB;gBACxB,yCAAyC;gBACzC,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,uBAAuB;gBACvB,cAAc;gBACd,OAAO;gBACP,wBAAwB;gBACxB,2BAA2B;gBAC3B,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,uBAAuB;gBACvB,cAAc;gBACd,OAAO;gBACP,wBAAwB;gBACxB,yCAAyC;gBACzC,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,uBAAuB;gBACvB,cAAc;gBACd,OAAO;gBACP,wBAAwB;gBACxB,8CAA8C;gBAC9C,OAAO;gBACP,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;YACjD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,KAAK,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC7C,MAAM,QAAQ,GAAG,kBAAkB,CAAC;IACpC,IAAI,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACxC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,KAAK;gBACL,6BAA6B;gBAC7B,yCAAyC;gBACzC,iBAAiB;gBACjB,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,KAAK;gBACL,6BAA6B;gBAC7B,yCAAyC;gBACzC,4BAA4B;gBAC5B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,CAAC;YACvD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACtC,CAAC;YACA,MAAM,OAAO,GAAG;gBACf,GAAG;gBACH,KAAK;gBACL,6BAA6B;gBAC7B,yCAAyC;gBACzC,6BAA6B;gBAC7B,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,UAAU,GAAG;gBAClB,GAAG;gBACH,KAAK;gBACL,6BAA6B;gBAC7B,yCAAyC;gBACzC,wBAAwB;gBACxB,KAAK;gBACL,GAAG;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YACnD,MAAM,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC;IACF,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAQH,KAAK,UAAU,cAAc,CAAC,YAAoB,EAAE,UAAkB,EAAE,OAAe,EAAE,QAAyB;IAEjH,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEvE,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;IAC1E,MAAM,aAAE,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAE3C,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACjE,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAEpD,iGAAiG;IACjG,MAAM,iBAAiB,GAAG,CAAC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sCAAsC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAA0B,CAAC;IAEpJ,MAAM,OAAO,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC3D,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC;IAC5C,CAAC,CAAC,CAAC;IACH,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;QAC3B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,iCAAiC,CAAC,CAAC;IAC3F,CAAC;SAAM,CAAC;QACP,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,uCAAuC,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEtJ,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBACrC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,YAAY,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;gBACxF,MAAM,IAAI,GAAG,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;gBAE9F,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBACjD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACP,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC5C,CAAC;QACF,CAAC;IACF,CAAC;AACF,CAAC;AAED,KAAK,UAAU,cAAc,CAAC,MAAkB,EAAE,UAAkB,EAAE,OAAe;IACpF,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,8BAA8B,CAAE,CAAC;IAC5E,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;IAErB,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC5D,MAAM,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IAChE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAEzD,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IACpG,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IACxD,OAAO,MAAM,CAAC;AAEf,CAAC", "file": "completion.test.js", "sourceRoot": "../../src/"}