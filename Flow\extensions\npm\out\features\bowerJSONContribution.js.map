{"version": 3, "sources": ["features/bowerJSONContribution.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAEhG,mCAAmI;AAMnI,MAAM,UAAU,GAAG,oBAAoB,CAAC;AAExC,MAAa,qBAAqB;IAYjC,YAAmB,GAAe;QAV1B,cAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ;YAC3K,sBAAsB,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,mBAAmB,EAAE,YAAY,EAAE,UAAU;YACnI,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ;YAC5K,eAAe,EAAE,eAAe,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa;YACpH,uBAAuB,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY;YAC5K,KAAK,EAAE,qBAAqB,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa;YACjL,OAAO,EAAE,cAAc,CAAC,CAAC;QAKzB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IAChB,CAAC;IAEM,mBAAmB;QACzB,OAAO,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACpI,CAAC;IAEO,SAAS;QAChB,OAAO,CAAC,CAAC,kBAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC1E,CAAC;IAEM,yBAAyB,CAAC,SAAc,EAAE,SAAgC;QAChF,MAAM,YAAY,GAAG;YACpB,MAAM,EAAE,WAAW;YACnB,aAAa,EAAE,kBAAkB;YACjC,SAAS,EAAE,CAAC,aAAa,CAAC;YAC1B,SAAS,EAAE,YAAY;YACvB,MAAM,EAAE,iBAAiB;YACzB,cAAc,EAAE,EAAE;SAClB,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,aAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;QAClE,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,KAAK,CAAC;QACzC,QAAQ,CAAC,UAAU,GAAG,IAAI,sBAAa,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAClF,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEM,0BAA0B,CAAC,SAAc,EAAE,QAAkB,EAAE,WAAmB,EAAE,QAAiB,EAAE,MAAe,EAAE,SAAgC;QAC9J,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;YACnF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,QAAQ,GAAG,4CAA4C,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBAEhG,OAAO,IAAI,CAAC,GAAG,CAAC;oBACf,GAAG,EAAE,QAAQ;oBACb,OAAO,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE;iBAC9B,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;oBACnB,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;wBAC5B,IAAI,CAAC;4BACJ,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;4BAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gCACxB,MAAM,OAAO,GAA4C,GAAG,CAAC;gCAC7D,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oCAC9B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;oCACzB,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;oCAC7C,MAAM,UAAU,GAAG,IAAI,sBAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oCACxE,IAAI,QAAQ,EAAE,CAAC;wCACd,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wCACxD,IAAI,CAAC,MAAM,EAAE,CAAC;4CACb,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wCAC5B,CAAC;oCACF,CAAC;oCACD,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,IAAI,CAAC,CAAC;oCAC1C,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;oCAC5C,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;oCACjC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oCAC3C,QAAQ,CAAC,aAAa,GAAG,WAAW,CAAC;oCACrC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gCACzB,CAAC;gCACD,SAAS,CAAC,eAAe,EAAE,CAAC;4BAC7B,CAAC;wBACF,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACZ,SAAS;wBACV,CAAC;oBACF,CAAC;yBAAM,CAAC;wBACP,SAAS,CAAC,KAAK,CAAC,aAAI,CAAC,CAAC,CAAC,6CAA6C,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;wBAC7F,OAAO,CAAC,CAAC;oBACV,CAAC;oBACD,OAAO,SAAS,CAAC;gBAClB,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE;oBACZ,SAAS,CAAC,KAAK,CAAC,aAAI,CAAC,CAAC,CAAC,6CAA6C,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;oBAC3F,OAAO,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC/B,MAAM,UAAU,GAAG,IAAI,sBAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oBACxE,IAAI,QAAQ,EAAE,CAAC;wBACd,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACxD,IAAI,CAAC,MAAM,EAAE,CAAC;4BACb,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAC5B,CAAC;oBACF,CAAC;oBAED,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,IAAI,CAAC,CAAC;oBAC1C,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;oBAC5C,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;oBACjC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC3C,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAC;oBAC5B,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC;gBACH,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,uBAAuB,CAAC,SAAc,EAAE,QAAkB,EAAE,SAAgC;QAClG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7F,gIAAgI;YAChI,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,aAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtD,QAAQ,CAAC,UAAU,GAAG,IAAI,sBAAa,CAAC,eAAe,CAAC,CAAC;YACzD,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;YAC3B,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,KAAK,CAAC;YACzC,QAAQ,CAAC,aAAa,GAAG,mCAAmC,CAAC;YAC7D,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,iBAAiB,CAAC,SAA0B,EAAE,IAAoB;QACxE,IAAI,IAAI,CAAC,IAAI,KAAK,2BAAkB,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,KAAK,EAAE,EAAE,CAAC;YAE5E,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACvB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC/B,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACrB,CAAC;YAED,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC/C,IAAI,aAAa,EAAE,CAAC;oBACnB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;oBACnC,OAAO,IAAI,CAAC;gBACb,CAAC;gBACD,OAAO,IAAI,CAAC;YACb,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,OAAO,CAAC,IAAY;QAC3B,MAAM,QAAQ,GAAG,qCAAqC,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAElF,OAAO,IAAI,CAAC,GAAG,CAAC;YACf,GAAG,EAAE,QAAQ;YACb,OAAO,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE;SAC9B,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACnB,IAAI,CAAC;gBACJ,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAC7C,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;oBACpB,IAAI,GAAG,GAAW,GAAG,CAAC,GAAG,CAAC;oBAC1B,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;wBACjC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACxB,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;wBAC9D,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACxC,CAAC;oBACD,OAAO,GAAG,CAAC;gBACZ,CAAC;YACF,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,SAAS;YACV,CAAC;YACD,OAAO,SAAS,CAAC;QAClB,CAAC,EAAE,GAAG,EAAE;YACP,OAAO,SAAS,CAAC;QAClB,CAAC,CAAC,CAAC;IACJ,CAAC;IAEM,mBAAmB,CAAC,SAAc,EAAE,QAAkB;QAC5D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7F,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACrD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;oBAC9C,IAAI,aAAa,EAAE,CAAC;wBACnB,MAAM,GAAG,GAAG,IAAI,uBAAc,EAAE,CAAC;wBACjC,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;wBAC9B,OAAO,CAAC,GAAG,CAAC,CAAC;oBACd,CAAC;oBACD,OAAO,IAAI,CAAC;gBACb,CAAC,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;CACD;AAlMD,sDAkMC", "file": "bowerJSONContribution.js", "sourceRoot": "../../src/"}