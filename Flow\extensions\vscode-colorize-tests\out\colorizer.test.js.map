{"version": 3, "sources": ["colorizer.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,uCAAyB;AACzB,iBAAe;AACf,+BAAuC;AACvC,mCAAuE;AAEvE,KAAK,UAAU,qBAAqB,CAAC,YAAoB,EAAE,WAAmB,EAAE,qBAA6B,EAAE,OAAe,EAAE,IAAS;IACxI,MAAM,cAAc,GAAG,IAAA,WAAI,EAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IACnD,MAAM,UAAU,GAAG,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,WAAW,EAAE,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAE7K,IAAI,CAAC;QACJ,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;YACpD,MAAM,IAAI,GAAG,MAAM,iBAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,YAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAExF,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3C,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACrC,CAAC;YACD,MAAM,UAAU,GAAG,IAAA,WAAI,EAAC,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;YACpF,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACxE,IAAI,CAAC;oBACJ,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;gBAC5C,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACZ,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;oBAC9E,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;wBAC/F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;4BACtC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;4BAClB,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;4BAC1B,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gCAC7C,MAAM,CAAC,CAAC;4BACT,CAAC;wBACF,CAAC;wBACD,4DAA4D;oBAC7D,CAAC;yBAAM,CAAC;wBACP,MAAM,CAAC,CAAC;oBACT,CAAC;gBACF,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YAChE,CAAC;QACF,CAAC,CAAC,CAAC,CAAC;QACJ,IAAI,EAAE,CAAC;IACR,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACZ,IAAI,CAAC,CAAC,CAAC,CAAC;IACT,CAAC;AACF,CAAC;AAED,SAAS,cAAc,CAAC,CAAM,EAAE,CAAM;IACrC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,CAAC;IACF,CAAC;IACD,OAAO,KAAK,CAAC;AACd,CAAC;AAED,KAAK,CAAC,cAAc,EAAE,GAAG,EAAE;IAC1B,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,IAAA,WAAI,EAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IACvD,MAAM,YAAY,GAAG,IAAA,WAAI,EAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;IACzD,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;IACvD,MAAM,qBAAqB,GAAG,IAAA,WAAI,EAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAC;IAC7E,IAAI,qBAA4B,CAAC;IAEjC,UAAU,CAAC,KAAK;QACf,qBAAqB,GAAG;YACvB,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,6BAA6B,CAAC;YACpF,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,sBAAsB,CAAC;YAC7E,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,wBAAwB,CAAC;YAC/E,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,sBAAsB,CAAC;SAC7E,CAAC;QACF,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,6BAA6B,EAAE,IAAI,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;QAChI,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,sBAAsB,EAAE,IAAI,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;QACzH,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,wBAAwB,EAAE,IAAI,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;QAC3H,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,sBAAsB,EAAE,IAAI,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;IAC1H,CAAC,CAAC,CAAC;IACH,aAAa,CAAC,KAAK;QAClB,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,6BAA6B,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;QACpJ,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,sBAAsB,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;QAC7I,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,wBAAwB,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;QAC/I,MAAM,kBAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,sBAAsB,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,4BAAmB,CAAC,MAAM,CAAC,CAAC;IAC9I,CAAC,CAAC,CAAC;IAEH,KAAK,MAAM,OAAO,IAAI,EAAE,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;QACpD,IAAI,CAAC,aAAa,OAAO,EAAE,EAAE,UAAU,IAAI;YAC1C,iBAAQ,CAAC,cAAc,CAAC,kCAAkC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACrE,qBAAqB,CAAC,YAAY,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC;AACF,CAAC,CAAC,CAAC", "file": "colorizer.test.js", "sourceRoot": "../src/"}