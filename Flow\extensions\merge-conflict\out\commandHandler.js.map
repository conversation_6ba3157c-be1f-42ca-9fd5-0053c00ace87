{"version": 3, "sources": ["commandHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;gGAGgG;AAChG,+CAAiC;AAEjC,wEAAgD;AAOhD,IAAK,mBAGJ;AAHD,WAAK,mBAAmB;IACvB,qEAAQ,CAAA;IACR,uEAAS,CAAA;AACV,CAAC,EAHI,mBAAmB,KAAnB,mBAAmB,QAGvB;AAED,MAAqB,cAAc;IAKlC,YAAY,cAA+D;QAHnE,gBAAW,GAAwB,EAAE,CAAC;QAI7C,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,KAAK;QACJ,IAAI,CAAC,WAAW,CAAC,IAAI,CACpB,IAAI,CAAC,yBAAyB,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAC,EACnF,IAAI,CAAC,yBAAyB,CAAC,gCAAgC,EAAE,IAAI,CAAC,cAAc,CAAC,EACrF,IAAI,CAAC,yBAAyB,CAAC,iCAAiC,EAAE,IAAI,CAAC,eAAe,CAAC,EACvF,IAAI,CAAC,yBAAyB,CAAC,4BAA4B,EAAE,IAAI,CAAC,UAAU,CAAC,EAC7E,IAAI,CAAC,yBAAyB,CAAC,mCAAmC,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,EAC1H,IAAI,CAAC,yBAAyB,CAAC,oCAAoC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,0BAA0B,CAAC,EAC7H,IAAI,CAAC,yBAAyB,CAAC,gCAAgC,EAAE,IAAI,CAAC,aAAa,CAAC,EACpF,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,IAAI,CAAC,YAAY,CAAC,EACxE,IAAI,CAAC,yBAAyB,CAAC,yBAAyB,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAChF,IAAI,CAAC,yBAAyB,CAAC,wBAAwB,EAAE,IAAI,CAAC,OAAO,CAAC,CACtE,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,OAAe,EAAE,EAAgE,EAAE,UAAkD;QACtK,OAAO,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE;YAC3D,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5E,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;YAChE,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAC9C,OAAO,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,MAAyB,EAAE,GAAG,IAAW;QACtD,OAAO,IAAI,CAAC,MAAM,wCAAgC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;IACpE,CAAC;IAED,cAAc,CAAC,MAAyB,EAAE,GAAG,IAAW;QACvD,OAAO,IAAI,CAAC,MAAM,yCAAiC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;IACrE,CAAC;IAED,UAAU,CAAC,MAAyB,EAAE,GAAG,IAAW;QACnD,OAAO,IAAI,CAAC,MAAM,qCAA6B,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;IACjE,CAAC;IAED,gBAAgB,CAAC,MAAyB;QACzC,OAAO,IAAI,CAAC,SAAS,wCAAgC,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED,iBAAiB,CAAC,MAAyB;QAC1C,OAAO,IAAI,CAAC,SAAS,yCAAiC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAED,yBAAyB,CAAC,SAAuB;QAChD,OAAO,IAAI,CAAC,kBAAkB,wCAAgC,SAAS,CAAC,CAAC;IAC1E,CAAC;IAED,0BAA0B,CAAC,SAAuB;QACjD,OAAO,IAAI,CAAC,kBAAkB,yCAAiC,SAAS,CAAC,CAAC;IAC3E,CAAC;IAED,aAAa,CAAC,MAAyB;QACtC,OAAO,IAAI,CAAC,SAAS,qCAA6B,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAyB,EAAE,QAAkD;QAE1F,qDAAqD;QACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,QAAQ,GAAG,MAAM,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;YAE9D,wDAAwD;YACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC,CAAC;gBAChG,OAAO;YACR,CAAC;QACF,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEnE,wDAAwD;QACxD,IAAI,CAAC,SAAS,EAAE,CAAC;YAChB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC,CAAC;YAChG,OAAO;QACR,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QAC1C,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;QACrC,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACzF,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAE3F,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;YACxC,MAAM,EAAE,yBAAe,CAAC,MAAM;YAC9B,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;SACnE,CAAC,CAAC;QAGH,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;QAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;QAE1F,IAAI,wBAAwB,GAAG,CAAC,CAAC;QACjC,KAAK,MAAM,YAAY,IAAI,SAAS,EAAE,CAAC;YACtC,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChD,MAAM;YACP,CAAC;iBAAM,CAAC;gBACP,wBAAwB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjL,CAAC;QACF,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CACjC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,wBAAwB,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EACpF,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,wBAAwB,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CACpF,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;QACzC,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kDAAkD;QACpH,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,yCAAyC,EAAE,QAAQ,CAAC,CAAC;QACjF,MAAM,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAChF,MAAM,aAAa,GAAG,mBAAmB,CAAC,GAAG,CAAS,kBAAkB,CAAC,CAAC;QAC1E,MAAM,IAAI,GAAmC;YAC5C,UAAU,EAAE,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM;YAC5F,SAAS;SACT,CAAC;QAEF,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;YAC/B,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACrF,CAAC;IAED,YAAY,CAAC,MAAyB;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED,gBAAgB,CAAC,MAAyB;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAyB;QAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;QAEpE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC,CAAC;YAChG,OAAO;QACR,CAAC;QAED,IAAI,YAAmC,CAAC;QACxC,IAAI,sBAAsB,GAAiB,QAAQ,CAAC,QAAQ,CAAC;QAE7D,IAAI,QAAQ,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,sBAAsB,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7D,CAAC;QAED,8EAA8E;QAC9E,6EAA6E;QAC7E,mEAAmE;QACnE,+EAA+E;QAC/E,oFAAoF;QACpF,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpE,YAAY,wCAAgC,CAAC;QAC9C,CAAC;aACI,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACjE,YAAY,yCAAiC,CAAC;QAC/C,CAAC;aACI,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACpE,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gHAAgH,CAAC,CAAC,CAAC;YAClK,OAAO;QACR,CAAC;aACI,CAAC;YACL,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,iHAAiH,CAAC,CAAC,CAAC;YACnK,OAAO;QACR,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACrC,QAAQ,CAAC,UAAU,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO;QACN,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,SAA8B;QAC/E,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEjF,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACvB,qHAAqH;YACrH,MAAM,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAChF,IAAI,mBAAmB,CAAC,GAAG,CAAU,kCAAkC,CAAC,EAAE,CAAC;gBAC1E,OAAO;YACR,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC;YACzF,OAAO;QACR,CAAC;aACI,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAC;YAC7F,OAAO;QACR,CAAC;aACI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YACrC,4BAA4B;YAC5B,OAAO;QACR,CAAC;QAED,uDAAuD;QACvD,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACtH,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC1F,CAAC;IAEO,KAAK,CAAC,MAAM,CAAC,IAA2B,EAAE,MAAyB,EAAE,GAAG,IAAW;QAE1F,IAAI,QAAkD,CAAC;QAEvD,8DAA8D;QAC9D,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE,CAAC;YAClC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;aACI,CAAC;YACL,sEAAsE;YACtE,QAAQ,GAAG,MAAM,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,8CAA8C,CAAC,CAAC,CAAC;YAChG,OAAO;QACR,CAAC;QAED,2DAA2D;QAC3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACrC,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElC,sCAAsC;QACtC,MAAM,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAChF,IAAI,mBAAmB,CAAC,GAAG,CAAU,kCAAkC,CAAC,EAAE,CAAC;YAC1E,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;IAEF,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAA2B,EAAE,MAAyB;QAC7E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEnE,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC;YACzF,OAAO;QACR,CAAC;QAED,wFAAwF;QACxF,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAErC,gCAAgC;QAChC,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACxD,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAA2B,EAAE,SAAuB;QACpF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5G,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QACxC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE5D,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1C,SAAS;YACV,CAAC;YAED,wFAAwF;YACxF,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE9B,gCAAgC;YAChC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC5B,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;YACjH,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,+BAA+B,CAAC,MAAyB,EAAE,SAA+C;QAEvH,IAAI,CAAC,SAAS,EAAE,CAAC;YAChB,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACb,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAClC,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtD,OAAO,QAAQ,CAAC;YACjB,CAAC;QACF,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,MAAyB,EAAE,SAA8B,EAAE,SAA+C;QACjJ,IAAI,CAAC,SAAS,EAAE,CAAC;YAChB,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;QAC1C,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,OAAO;oBACN,WAAW,EAAE,KAAK;iBAClB,CAAC;YACH,CAAC;YAED,OAAO;gBACN,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;aACtB,CAAC;QACH,CAAC;QAED,IAAI,SAAsC,CAAC;QAC3C,IAAI,QAAiD,CAAC;QACtD,IAAI,SAA8C,CAAC;QAEnD,IAAI,SAAS,KAAK,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YAChD,SAAS,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnE,QAAQ,GAAG,GAAG,EAAE,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,SAAS,GAAG,SAAS,CAAC;QACvB,CAAC;aAAM,IAAI,SAAS,KAAK,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACxD,SAAS,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAClE,QAAQ,GAAG,GAAG,EAAE,CAAC,SAAU,CAAC,SAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnD,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC;aAAM,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,yBAAyB,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAClC,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAChE,OAAO;oBACN,WAAW,EAAE,IAAI;oBACjB,QAAQ,EAAE,QAAQ;iBAClB,CAAC;YACH,CAAC;QACF,CAAC;QAED,+CAA+C;QAC/C,OAAO;YACN,WAAW,EAAE,IAAI;YACjB,QAAQ,EAAE,QAAQ,EAAE;SACpB,CAAC;IACH,CAAC;CACD;AA9VD,iCA8VC", "file": "commandHandler.js", "sourceRoot": "../src/"}