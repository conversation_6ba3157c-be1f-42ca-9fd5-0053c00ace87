"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.terminateProcess = terminateProcess;
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
const cp = __importStar(require("child_process"));
const path = __importStar(require("path"));
function terminateProcess(p, extensionPath) {
    if (process.platform === 'win32') {
        try {
            const options = {
                stdio: ['pipe', 'pipe', 'ignore']
            };
            cp.execFileSync('taskkill', ['/T', '/F', '/PID', p.pid.toString()], options);
        }
        catch (err) {
            return { success: false, error: err };
        }
    }
    else if (process.platform === 'darwin' || process.platform === 'linux') {
        try {
            const cmd = path.join(extensionPath, 'scripts', 'terminateProcess.sh');
            const result = cp.spawnSync(cmd, [p.pid.toString()]);
            if (result.error) {
                return { success: false, error: result.error };
            }
        }
        catch (err) {
            return { success: false, error: err };
        }
    }
    else {
        p.kill('SIGKILL');
    }
    return { success: true };
}
//# sourceMappingURL=processes.js.map