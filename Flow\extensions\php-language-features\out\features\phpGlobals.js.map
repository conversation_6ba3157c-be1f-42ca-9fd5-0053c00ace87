{"version": 3, "sources": ["features/phpGlobals.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAOnF,QAAA,eAAe,GAAa;IACxC,QAAQ,EAAE;QACT,WAAW,EAAE,0KAA0K;KACvL;IACD,QAAQ,EAAE;QACT,WAAW,EAAE,yZAAyZ;KACta;IACD,KAAK,EAAE;QACN,WAAW,EAAE,wFAAwF;KACrG;IACD,MAAM,EAAE;QACP,WAAW,EAAE,0FAA0F;KACvG;IACD,OAAO,EAAE;QACR,WAAW,EAAE,wFAAwF;KACrG;IACD,SAAS,EAAE;QACV,WAAW,EAAE,2FAA2F;KACxG;IACD,SAAS,EAAE;QACV,WAAW,EAAE,sKAAsK;KACnL;IACD,KAAK,EAAE;QACN,WAAW,EAAE,mmBAAmmB;KAChnB;IACD,QAAQ,EAAE;QACT,WAAW,EAAE,kFAAkF;KAC/F;IACD,aAAa,EAAE;QACd,WAAW,EAAE,qQAAqQ;KAClR;IACD,mBAAmB,EAAE;QACpB,WAAW,EAAE,mFAAmF;KAChG;IACD,qBAAqB,EAAE;QACtB,WAAW,EAAE,0OAA0O;KACvP;IACD,KAAK,EAAE;QACN,WAAW,EAAE,mGAAmG;KAChH;IACD,KAAK,EAAE;QACN,WAAW,EAAE,iGAAiG;KAC9G;IACD,KAAK,EAAE;QACN,WAAW,EAAE,8BAA8B;KAC3C;CACD,CAAC;AACW,QAAA,oBAAoB,GAAa;IAC7C,SAAS,EAAE;QACV,WAAW,EAAE,qKAAqK;KAClL;IACD,OAAO,EAAE;QACR,WAAW,EAAE,0PAA0P;KACvQ;IACD,QAAQ,EAAE;QACT,WAAW,EAAE,gRAAgR;KAC7R;IACD,YAAY,EAAE;QACb,WAAW,EAAE,2KAA2K;KACxL;IACD,QAAQ,EAAE;QACT,WAAW,EAAE,sCAAsC;KACnD;IACD,UAAU,EAAE;QACX,WAAW,EAAE,8GAA8G;KAC3H;IACD,aAAa,EAAE;QACd,WAAW,EAAE,oHAAoH;KACjI;IACD,IAAI,EAAE,EACL;IACD,KAAK,EAAE,EACN;IACD,IAAI,EAAE,EACL;IACD,IAAI,EAAE;QACL,WAAW,EAAE,yCAAyC;KACtD;IACD,GAAG,EAAE;QACJ,WAAW,EAAE,uCAAuC;KACpD;IACD,OAAO,EAAE;QACR,WAAW,EAAE,6CAA6C;KAC1D;IACD,QAAQ,EAAE;QACT,WAAW,EAAE,+CAA+C;KAC5D;IACD,KAAK,EAAE;QACN,WAAW,EAAE,8CAA8C;KAC3D;IACD,MAAM,EAAE;QACP,WAAW,EAAE,+CAA+C;KAC5D;IACD,MAAM,EAAE;QACP,WAAW,EAAE,2CAA2C;KACxD;IACD,MAAM,EAAE;QACP,WAAW,EAAE,2CAA2C;KACxD;IACD,MAAM,EAAE;QACP,WAAW,EAAE,2CAA2C;KACxD;IACD,MAAM,EAAE;QACP,WAAW,EAAE,2CAA2C;KACxD;IACD,QAAQ,EAAE;QACT,WAAW,EAAE,+CAA+C;KAC5D;IACD,UAAU,EAAE;QACX,WAAW,EAAE,iDAAiD;KAC9D;IACD,OAAO,EAAE;QACR,WAAW,EAAE,8CAA8C;KAC3D;IACD,OAAO,EAAE;QACR,WAAW,EAAE,8CAA8C;KAC3D;IACD,SAAS,EAAE;QACV,WAAW,EAAE,+CAA+C;KAC5D;IACD,MAAM,EAAE;QACP,WAAW,EAAE,gDAAgD;KAC7D;IACD,OAAO,EAAE;QACR,WAAW,EAAE,wCAAwC;KACrD;IACD,iBAAiB,EAAE;QAClB,WAAW,EAAE,qBAAqB;KAClC;IACD,mBAAmB,EAAE;QACpB,WAAW,EAAE,uBAAuB;KACpC;IACD,mBAAmB,EAAE;QACpB,WAAW,EAAE,kCAAkC;KAC/C;IACD,kBAAkB,EAAE;QACnB,WAAW,EAAE,gCAAgC;KAC7C;IACD,GAAG,EAAE;QACJ,WAAW,EAAE,gCAAgC;KAC7C;IACD,GAAG,EAAE;QACJ,WAAW,EAAE,gCAAgC;KAC7C;IACD,eAAe,EAAE;QAChB,WAAW,EAAE,2FAA2F;KACxG;IACD,gBAAgB,EAAE;QACjB,WAAW,EAAE,oKAAoK;KACjL;CACD,CAAC;AACW,QAAA,QAAQ,GAAa;IACjC,MAAM,EAAE;QACP,WAAW,EAAE,sCAAsC;QACnD,SAAS,EAAE,2EAA2E;KACtF;IACD,GAAG,EAAE;QACJ,WAAW,EAAE,kDAAkD;KAC/D;IACD,IAAI,EAAE;QACL,WAAW,EAAE,wlBAAwlB;QACrmB,SAAS,EAAE,yCAAyC;KACpD;IACD,KAAK,EAAE;QACN,WAAW,EAAE,yDAAyD;QACtE,SAAS,EAAE,sBAAsB;KACjC;IACD,IAAI,EAAE;QACL,WAAW,EAAE,iIAAiI;QAC9I,SAAS,EAAE,0DAA0D;KACrE;IACD,IAAI,EAAE;QACL,WAAW,EAAE,onBAAonB;QACjoB,SAAS,EAAE,6BAA6B;KACxC;IACD,OAAO,EAAE;QACR,WAAW,EAAE,kEAAkE;KAC/E;IACD,YAAY,EAAE;QACb,WAAW,EAAE,glBAAglB;KAC7lB;IACD,KAAK,EAAE;QACN,WAAW,EAAE,2dAA2d;QACxe,SAAS,EAAE,sCAAsC;KACjD;IACD,IAAI,EAAE;QACL,WAAW,EAAE,uIAAuI;QACpJ,SAAS,EAAE,2CAA2C;KACtD;IACD,OAAO,EAAE;QACR,WAAW,EAAE,6OAA6O;KAC1P;IACD,YAAY,EAAE;QACb,WAAW,EAAE,4JAA4J;KACzK;IACD,MAAM,EAAE;QACP,WAAW,EAAE,gzBAAgzB;KAC7zB;IACD,KAAK,EAAE;QACN,WAAW,EAAE,8JAA8J;QAC3K,SAAS,EAAE,sBAAsB;KACjC;IACD,KAAK,EAAE;QACN,WAAW,EAAE,oXAAoX;QACjY,SAAS,EAAE,sCAAsC;KACjD;IACD,KAAK,EAAE;QACN,WAAW,EAAE,oUAAoU;KACjV;IACD,QAAQ,EAAE,EACT;IACD,GAAG,EAAE,EACJ;IACD,KAAK,EAAE,EACN;IACD,EAAE,EAAE,EACH;IACD,KAAK,EAAE,EACN;IACD,IAAI,EAAE,EACL;IACD,KAAK,EAAE,EACN;IACD,KAAK,EAAE,EACN;IACD,KAAK,EAAE,EACN;IACD,KAAK,EAAE,EACN;IACD,QAAQ,EAAE,EACT;IACD,OAAO,EAAE,EACR;IACD,OAAO,EAAE,EACR;IACD,EAAE,EAAE,EACH;IACD,IAAI,EAAE,EACL;IACD,MAAM,EAAE,EACP;IACD,UAAU,EAAE,EACX;IACD,MAAM,EAAE,EACP;IACD,UAAU,EAAE,EACX;IACD,KAAK,EAAE,EACN;IACD,SAAS,EAAE,EACV;IACD,QAAQ,EAAE,EACT;IACD,OAAO,EAAE,EACR;IACD,KAAK,EAAE,EACN;IACD,OAAO,EAAE,EACR;IACD,GAAG,EAAE,EACJ;IACD,OAAO,EAAE,EACR;IACD,QAAQ,EAAE,EACT;IACD,MAAM,EAAE,EACP;IACD,IAAI,EAAE,EACL;IACD,EAAE,EAAE,EACH;IACD,UAAU,EAAE,EACX;IACD,SAAS,EAAE,EACV;IACD,UAAU,EAAE,EACX;IACD,SAAS,EAAE,EACV;IACD,SAAS,EAAE,EACV;IACD,GAAG,EAAE,EACJ;IACD,EAAE,EAAE,EACH;IACD,MAAM,EAAE,EACP;IACD,OAAO,EAAE,EACR;IACD,SAAS,EAAE,EACV;IACD,MAAM,EAAE,EACP;IACD,IAAI,EAAE,EACL;IACD,MAAM,EAAE,EACP;IACD,MAAM,EAAE,EACP;IACD,KAAK,EAAE,EACN;IACD,KAAK,EAAE,EACN;IACD,GAAG,EAAE,EACJ;IACD,GAAG,EAAE,EACJ;IACD,GAAG,EAAE,EACJ;IACD,KAAK,EAAE,EACN;IACD,GAAG,EAAE,EACJ;CACD,CAAC", "file": "phpGlobals.js", "sourceRoot": "../../src/"}