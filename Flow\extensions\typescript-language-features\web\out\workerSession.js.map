{"version": 3, "sources": ["workerSession.ts"], "names": [], "mappings": ";;AAwBA,gDAqGC;AAtHD,0EAAgF;AAChF,0CAAuC;AACvC,mEAAgE;AAehE,SAAgB,kBAAkB,CACjC,EAAmD,EACnD,IAA0B,EAC1B,EAA0B,EAC1B,OAA4B,EAC5B,IAAiB,EACjB,UAAsB,EACtB,MAAc;IAEd,MAAM,MAAM,GAA6B,EAAU,CAAC,MAAM,CAAC,MAAM,CAAC;IAElE,MAAM,MAAM,GAAG,IAAI,MAAM,aAAc,SAAQ,EAAE,CAAC,MAAM,CAAC,OAAW;QAKnE;YACC,MAAM,iBAAiB,GAAG,IAAI,6CAAqB,EAAE,CAAC;YACtD,MAAM,gBAAgB,GAAG,OAAO,CAAC,iCAAiC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,4CAAyB,CAAC,IAAI,EAAE,kDAAkD,CAAC,CAAC;YAErM,KAAK,CAAC;gBACL,IAAI;gBACJ,iBAAiB;gBACjB,GAAG,OAAO;gBACV,gBAAgB;gBAChB,UAAU,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,8FAA8F;gBACzJ,MAAM,EADqD,8FAA8F;gBACzJ,eAAM;gBACN,MAAM,EAAE,MAAM,CAAC,QAAQ;gBACvB,YAAY,EAAE,IAAI;aAClB,CAAC,CAAC;YACH,IAAI,CAAC,qBAAqB,GAAG,iBAAiB,CAAC;YAE/C,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAY,EAAE,EAAE;gBAChC,2DAA2D;gBAC3D,SAAS,cAAc,CAAC,IAAS;oBAChC,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,YAAY,iBAAiB,CAAC,EAAE,CAAC;wBAC/F,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC;oBACpB,CAAC;oBACD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAChE,OAAO,GAAG,EAAE;wBACX,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;oBAC1C,CAAC,CAAC;gBACH,CAAC;gBAED,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,YAAY,EAAE,CAAC;oBAClB,IAAI,CAAC,qBAAqB,CAAC,YAAY,GAAG,YAAY,CAAC;gBACxD,CAAC;gBAED,IAAI,CAAC;oBACJ,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK,YAAY,EAAE,CAAC;wBAC3C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,SAAqD,CAAC;wBAChF,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,CAAC;4BACzC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gCAC1B,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;4BACjD,CAAC;wBACF,CAAC;oBACF,CAAC;gBACF,CAAC;gBAAC,MAAM,CAAC;oBACR,OAAO;gBACR,CAAC;gBAED,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC,CAAC;QACH,CAAC;QAEe,IAAI,CAAC,GAA+B;YACnD,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACtD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mDAAmD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC5F,CAAC;gBACD,OAAO;YACR,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;YAChE,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;QAEkB,YAAY,CAAC,OAAW;YAC1C,OAAO,OAAqC,CAAC;QAC9C,CAAC;QAEkB,eAAe,CAAC,OAAW;YAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;QAEQ,IAAI;YACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/B,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,EAAE,CAAC;QACT,CAAC;QAED,MAAM;YACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;YAC3F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,CAAC;KACD,EAAE,CAAC;IAEJ,MAAM,CAAC,MAAM,EAAE,CAAC;AACjB,CAAC", "file": "workerSession.js", "sourceRoot": "../src/"}