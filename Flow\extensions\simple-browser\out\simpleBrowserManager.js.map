{"version": 3, "sources": ["simpleBrowserManager.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAGhG,2DAAqE;AAErE,MAAa,oBAAoB;IAIhC,YACkB,YAAwB;QAAxB,iBAAY,GAAZ,YAAY,CAAY;IACtC,CAAC;IAEL,OAAO;QACN,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;IAC9B,CAAC;IAEM,IAAI,CAAC,QAA6B,EAAE,OAAqB;QAC/D,MAAM,GAAG,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9E,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACP,MAAM,IAAI,GAAG,qCAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;YACvE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YAEpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACzB,CAAC;IACF,CAAC;IAEM,OAAO,CAAC,KAA0B,EAAE,KAAU;QACpD,MAAM,GAAG,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,qCAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,WAAW,KAAhB,IAAI,CAAC,WAAW,GAAK,IAAI,EAAC;IAC3B,CAAC;IAEO,wBAAwB,CAAC,IAAuB;QACvD,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;YACnB,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;YAC9B,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CAED;AAxCD,oDAwCC", "file": "simpleBrowserManager.js", "sourceRoot": "../src/"}