{"version": 3, "sources": ["extensionEditingMain.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhG,4BASC;AAbD,+CAAiC;AACjC,mEAA0D;AAC1D,uDAAoD;AAEpD,SAAgB,QAAQ,CAAC,OAAgC;IAExD,0BAA0B;IAC1B,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,kCAAkC,EAAE,CAAC,CAAC;IAEjE,6CAA6C;IAC7C,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAC;IAE1D,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,iCAAe,EAAE,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,kCAAkC;IAC1C,OAAO,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,EAAE;QACxG,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK;YAC/C,OAAO,IAAI,uCAAe,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;KACD,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B;IACnC,OAAO,MAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,EAAE;QACrG,kBAAkB,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK;YACjD,OAAO,IAAI,uCAAe,CAAC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAChF,CAAC;KACD,CAAC,CAAC;AACJ,CAAC", "file": "extensionEditingMain.js", "sourceRoot": "../src/"}