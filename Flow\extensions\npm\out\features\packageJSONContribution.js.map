{"version": 3, "sources": ["features/packageJSONContribution.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,mCAAmI;AAKnI,kDAAoC;AACpC,+BAA+B;AAC/B,iCAAiC;AAEjC,MAAM,KAAK,GAAG,EAAE,CAAC;AAEjB,MAAM,UAAU,GAAG,oBAAoB,CAAC;AAExC,MAAa,uBAAuB;IAW5B,mBAAmB;QACzB,OAAO,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,YAA2B,GAAe,EAAU,cAAkC;QAA3D,QAAG,GAAH,GAAG,CAAY;QAAU,mBAAc,GAAd,cAAc,CAAoB;QAb9E,mBAAc,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,eAAe;YAC7I,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW;YACnJ,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ;YAC3I,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO;YAC/I,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS;YAClJ,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEjG,gBAAW,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IAOpF,CAAC;IAEM,yBAAyB,CAAC,SAAc,EAAE,MAA6B;QAC7E,MAAM,YAAY,GAAG;YACpB,MAAM,EAAE,WAAW;YACnB,aAAa,EAAE,kBAAkB;YACjC,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,YAAY;YACvB,MAAM,EAAE,iBAAiB;YACzB,cAAc,EAAE,EAAE;SAClB,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,aAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACpE,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,MAAM,CAAC;QAC1C,QAAQ,CAAC,UAAU,GAAG,IAAI,sBAAa,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAClF,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEO,SAAS;QAChB,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;IACpD,CAAC;IAEO,aAAa;QACpB,OAAO,CAAC,CAAC,kBAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC1E,CAAC;IAEM,0BAA0B,CAChC,SAAc,EACd,QAAkB,EAClB,WAAmB,EACnB,QAAiB,EACjB,MAAe,EACf,SAAgC;QAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3K,IAAI,QAAgB,CAAC;YACrB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBAC5B,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBACrC,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;oBAC7E,CAAC;oBACD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;wBACtC,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,KAAK,CAAC,CAAC;wBAC3C,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;wBAC5C,QAAQ,CAAC,UAAU,GAAG,IAAI,sBAAa,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBACnG,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;wBAC5C,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAC;wBAC5B,QAAQ,CAAC,OAAO,GAAG;4BAClB,KAAK,EAAE,EAAE;4BACT,OAAO,EAAE,8BAA8B;yBACvC,CAAC;wBACF,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACzB,CAAC;oBACD,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC7B,CAAC;gBAED,QAAQ,GAAG,+CAA+C,KAAK,SAAS,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC1G,OAAO,IAAI,CAAC,GAAG,CAAC;oBACf,GAAG,EAAE,QAAQ;oBACb,OAAO,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE;iBAC9B,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;oBACnB,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;wBAC5B,IAAI,CAAC;4BACJ,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;4BAC7C,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gCACtD,MAAM,OAAO,GAAqC,GAAG,CAAC,OAAO,CAAC;gCAC9D,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oCAC9B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;gCAClE,CAAC;4BAEF,CAAC;wBACF,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACZ,SAAS;wBACV,CAAC;wBACD,SAAS,CAAC,eAAe,EAAE,CAAC;oBAC7B,CAAC;yBAAM,CAAC;wBACP,SAAS,CAAC,KAAK,CAAC,aAAI,CAAC,CAAC,CAAC,2CAA2C,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;wBAC3F,OAAO,CAAC,CAAC;oBACV,CAAC;oBACD,OAAO,SAAS,CAAC;gBAClB,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE;oBACZ,SAAS,CAAC,KAAK,CAAC,aAAI,CAAC,CAAC,CAAC,2CAA2C,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;oBACzF,OAAO,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACpC,MAAM,UAAU,GAAG,IAAI,sBAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oBACxE,IAAI,QAAQ,EAAE,CAAC;wBACd,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAC7D,IAAI,CAAC,MAAM,EAAE,CAAC;4BACb,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAC5B,CAAC;oBACF,CAAC;oBACD,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,IAAI,CAAC,CAAC;oBAC1C,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;oBAC5C,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;oBACjC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC3C,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAC;oBAC5B,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;gBACrE,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,qBAAqB,CAAC,WAAmB,EAAE,QAAiB,EAAE,MAAe,EAAE,SAAgC;QACtH,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,IAAI,GAAG,EAAE,CAAC;YACX,CAAC;YACD,MAAM,QAAQ,GAAG,qDAAqD,KAAK,MAAM,IAAI,WAAW,CAAC;YACjG,OAAO,IAAI,CAAC,GAAG,CAAC;gBACf,GAAG,EAAE,QAAQ;gBACb,OAAO,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE;aAC9B,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;gBACnB,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC5B,IAAI,CAAC;wBACJ,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC7C,IAAI,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;4BACvC,MAAM,OAAO,GAAqC,GAAG,CAAC,OAAO,CAAC;4BAC9D,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gCAC9B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;4BAClE,CAAC;wBACF,CAAC;oBACF,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACZ,SAAS;oBACV,CAAC;oBACD,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACP,SAAS,CAAC,KAAK,CAAC,aAAI,CAAC,CAAC,CAAC,2CAA2C,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;gBAC5F,CAAC;gBACD,OAAO,IAAI,CAAC;YACb,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAAC,QAAa,EAAE,QAAkB,EAAE,MAA6B;QACpG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/L,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3D,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;gBACpC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAC/D,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAE1B,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACxC,IAAI,QAAQ,GAAG,IAAI,uBAAc,CAAC,IAAI,CAAC,CAAC;oBACxC,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;oBAC5C,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC3B,QAAQ,CAAC,aAAa,GAAG,aAAI,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC;oBAC/E,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAErB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC1C,QAAQ,GAAG,IAAI,uBAAc,CAAC,IAAI,CAAC,CAAC;oBACpC,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;oBAC5C,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC3B,QAAQ,CAAC,aAAa,GAAG,aAAI,CAAC,CAAC,CAAC,+CAA+C,CAAC,CAAC;oBACjF,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAErB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC1C,QAAQ,GAAG,IAAI,uBAAc,CAAC,IAAI,CAAC,CAAC;oBACpC,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;oBAC5C,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC3B,QAAQ,CAAC,aAAa,GAAG,aAAI,CAAC,CAAC,CAAC,+CAA+C,CAAC,CAAC;oBACjF,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACtB,CAAC;YACF,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,gBAAgB,CAAC,WAA+B,EAAE,OAA2B,EAAE,IAAwB,EAAE,QAA4B;QAC5I,MAAM,GAAG,GAAG,IAAI,uBAAc,EAAE,CAAC;QACjC,IAAI,WAAW,EAAE,CAAC;YACjB,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACb,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACvB,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,aAAI,CAAC,CAAC,CAAC,mCAAmC,EAAE,OAAO,EAAE,IAAA,cAAO,EAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAI,CAAC,CAAC,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC,CAAC;QAC7J,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACd,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACvB,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,GAAG,CAAC;IACZ,CAAC;IAEM,iBAAiB,CAAC,QAAyB,EAAE,IAAoB;QACvE,IAAI,IAAI,CAAC,IAAI,KAAK,2BAAkB,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAEtE,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;YACtB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;YACnB,CAAC;YAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACxD,IAAI,IAAI,EAAE,CAAC;oBACV,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACrG,OAAO,IAAI,CAAC;gBACb,CAAC;gBACD,OAAO,IAAI,CAAC;YACb,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,cAAc,CAAC,IAAY;QAClC,yEAAyE;QACzE,qDAAqD;QACrD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,OAAO,KAAK,CAAC;QACd,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACxE,IAAI,KAAK,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,KAAK,IAAI,kBAAkB,CAAC,KAAK,CAAC,KAAK,KAAK,EAAE,CAAC;gBAClD,OAAO,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,OAAO,kBAAkB,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC;QAC1C,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,QAAyB;QACrE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,SAAS,CAAC,CAAC,4BAA4B;QAC/C,CAAC;QACD,IAAI,IAAiC,CAAC;QACtC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YACnC,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,OAAO,CAAC,cAAsB,EAAE,IAAY,EAAE,QAAyB;QAC9E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YACvC,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAC9G,MAAM,GAAG,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAE1F,uFAAuF;YACvF,oEAAoE;YACpE,kEAAkE;YAClE,uEAAuE;YACvE,MAAM,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,wBAAwB,EAAE,GAAG,EAAE,4BAA4B,EAAE,GAAG,EAAE,CAAC;YACjG,IAAI,OAAO,GAAuB,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YAC/C,IAAI,WAAW,GAAW,cAAc,CAAC;YACzC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBAClC,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;gBACpC,WAAW,GAAG,IAAI,cAAc,GAAG,CAAC;YACrC,CAAC;YACD,EAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBACzD,IAAI,CAAC,KAAK,EAAE,CAAC;oBACZ,IAAI,CAAC;wBACJ,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBACnC,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;wBAClE,OAAO,CAAC;4BACP,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC;4BACnC,OAAO;4BACP,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC;4BAC7B,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC;yBAC7B,CAAC,CAAC;wBACH,OAAO;oBACR,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACZ,SAAS;oBACV,CAAC;gBACF,CAAC;gBACD,OAAO,CAAC,SAAS,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,IAAY;QACnC,MAAM,QAAQ,GAAG,6BAA6B,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAI,CAAC;YACJ,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC;gBAC9B,GAAG,EAAE,QAAQ;gBACb,OAAO,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE;aAC9B,CAAC,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,EAAE,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;YAClF,OAAO;gBACN,WAAW,EAAE,GAAG,CAAC,WAAW,IAAI,EAAE;gBAClC,OAAO;gBACP,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC;gBACzB,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,EAAE;aAC5B,CAAC;QACH,CAAC;QACD,OAAO,CAAC,EAAE,CAAC;YACV,QAAQ;QACT,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAEM,mBAAmB,CAAC,QAAa,EAAE,QAAkB;QAC3D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/L,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACrD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACxD,IAAI,IAAI,EAAE,CAAC;wBACV,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;oBAC1F,CAAC;oBACD,OAAO,IAAI,CAAC;gBACb,CAAC,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,cAAc,CAAC,IAAuB,EAAE,QAAiB,EAAE,MAAe,EAAE,SAAgC;QACnH,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,sBAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YACxE,IAAI,QAAQ,EAAE,CAAC;gBACd,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC7B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAClB,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACP,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC5B,CAAC;gBACD,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;oBACb,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC5B,CAAC;YACF,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,uBAAc,CAAC,IAAI,CAAC,CAAC;YAC1C,QAAQ,CAAC,IAAI,GAAG,2BAAkB,CAAC,QAAQ,CAAC;YAC5C,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;YACjC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC3C,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACjH,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;IACF,CAAC;CACD;AAhXD,0DAgXC", "file": "packageJSONContribution.js", "sourceRoot": "../../src/"}